<!DOCTYPE html>
<html lang="zh-CN" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Space and Time (SXT) 交互式投资分析仪表盘</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!-- Chosen Palette: Calm Neutrals (slate, stone, sky) -->
    <!-- Application Structure Plan: 本应用采用单页仪表盘式布局，旨在引导用户完成一个从“宏观概览”到“深度探究”的认知旅程。顶部导航栏实现快速区域跳转。开篇即以“中立”评级和核心“潜力vs风险”卡片定下分析基调，强制用户第一时间了解关键决策点。随后，各模块（技术、市场、代币、风险、策略）以可视化的方式（交互图、图表、卡片）层层展开，将报告中的复杂信息拆解为易于消化的单元。这种非线性的探索结构，相比传统报告，更能激发用户的互动，并根据个人兴趣点深入挖掘，最终目标是让用户在全面了解“潜力”与“风险”后，做出更明智的决策。 -->
    <!-- Visualization & Content Choices: 1. **评级卡片 (HTML/CSS)**: 目标-告知。在页面最顶部突出显示最终投资评级，作为整个分析的结论先行。 2. **潜力vs风险对比 (HTML/CSS)**: 目标-对比。将项目的核心优缺点并置，直观展示投资的核心矛盾。 3. **技术架构图 (HTML/CSS/JS)**: 目标-组织/告知。通过点击交互的流程图展示复杂的技术堆栈，比静态图片更具探索性。 4. **代币分配图 (Chart.js Donut)**: 目标-告知/比例。使用甜甜圈图清晰展示各方代币份额。 5. **竞品对比图 (Chart.js Bar)**: 目标-对比。用条形图量化对比SXT与竞品在关键维度的差异。 6. **解锁时间线 (HTML/CSS)**: 目标-展示变化。以视觉时间轴突出关键的代币解锁事件及其潜在市场冲击。 7. **投资策略表 (HTML/CSS/JS)**: 目标-组织/对比。采用交互式表格，清晰对比不同投资路径的风险、收益和门槛。所有可视化均未使用SVG或Mermaid.js，以满足约束条件。 -->
    <!-- CONFIRMATION: NO SVG graphics used. NO Mermaid JS used. -->
    <style>
        body { font-family: 'Noto Sans SC', sans-serif; background-color: #f8fafc; }
        .chart-container { position: relative; width: 100%; max-width: 600px; margin-left: auto; margin-right: auto; height: 300px; max-height: 400px; }
        @media (min-width: 768px) { .chart-container { height: 350px; } }
        .section-title { font-size: 1.875rem; font-weight: 700; color: #1e293b; border-bottom: 3px solid #38bdf8; padding-bottom: 0.5rem; margin-bottom: 2rem; }
        .card { background-color: white; border-radius: 0.75rem; box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1); padding: 1.5rem; transition: transform 0.3s, box-shadow 0.3s; }
        .card:hover { transform: translateY(-5px); box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -2px rgb(0 0 0 / 0.1); }
        .pill { display: inline-block; padding: 0.25rem 0.75rem; border-radius: 9999px; font-weight: 500; font-size: 0.875rem; }
        .nav-link { padding: 0.5rem 1rem; color: #475569; font-weight: 500; border-radius: 0.375rem; transition: background-color 0.2s, color 0.2s; }
        .nav-link:hover, .nav-link.active { background-color: #e0f2fe; color: #0c4a6e; }
        .tooltip { visibility: hidden; background-color: #1e293b; color: white; text-align: center; border-radius: 6px; padding: 5px 10px; position: absolute; z-index: 10; bottom: 125%; left: 50%; margin-left: -60px; opacity: 0; transition: opacity 0.3s; width: 120px; font-size: 0.8rem;}
        .has-tooltip:hover .tooltip { visibility: visible; opacity: 1; }
    </style>
</head>
<body class="text-slate-700">

    <header class="bg-white/80 backdrop-blur-lg sticky top-0 z-50 shadow-sm">
        <nav class="container mx-auto px-4">
            <div class="flex items-center justify-between h-16">
                <div class="flex items-center space-x-2">
                    <span class="text-2xl font-bold text-slate-800">SXT</span>
                    <span class="text-slate-500 hidden md:block">投资分析仪表盘</span>
                </div>
                <div class="hidden md:flex items-center space-x-2">
                    <a href="#summary" class="nav-link">投资摘要</a>
                    <a href="#tech" class="nav-link">核心技术</a>
                    <a href="#market" class="nav-link">市场对比</a>
                    <a href="#tokenomics" class="nav-link">代币经济</a>
                    <a href="#risks" class="nav-link">风险剖析</a>
                    <a href="#strategies" class="nav-link">投资策略</a>
                </div>
                <button id="mobile-menu-button" class="md:hidden p-2 rounded-md text-slate-500 hover:bg-slate-100">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16m-7 6h7"></path></svg>
                </button>
            </div>
            <div id="mobile-menu" class="hidden md:hidden pb-4">
                <a href="#summary" class="block nav-link text-center">投资摘要</a>
                <a href="#tech" class="block nav-link text-center">核心技术</a>
                <a href="#market" class="block nav-link text-center">市场对比</a>
                <a href="#tokenomics" class="block nav-link text-center">代币经济</a>
                <a href="#risks" class="block nav-link text-center">风险剖析</a>
                <a href="#strategies" class="block nav-link text-center">投资策略</a>
            </div>
        </nav>
    </header>

    <main class="container mx-auto p-4 md:p-8">

        <section id="summary" class="pt-8 mb-16">
            <div class="text-center mb-12">
                <h1 class="text-4xl md:text-5xl font-bold text-slate-900">Space and Time (SXT)</h1>
                <p class="text-lg text-slate-500 mt-2">一个关于“革命性技术”与“关键性风险”的投资博弈</p>
            </div>

            <div class="grid md:grid-cols-3 gap-8 items-start">
                <div class="md:col-span-1 card text-center">
                    <h3 class="text-lg font-semibold text-slate-800 mb-2">综合投资评级</h3>
                    <div class="pill bg-amber-100 text-amber-800 text-3xl font-bold mx-auto">中立</div>
                    <p class="text-sm text-slate-500 mt-4">在已知的“关键”级安全漏洞得到明确解决和修复前，任何投资都伴随着极高的不确定性。项目的巨大潜力被同等级别的风险所制衡。</p>
                </div>

                <div class="md:col-span-2 grid sm:grid-cols-2 gap-8">
                    <div class="card border-t-4 border-sky-400">
                        <h3 class="text-xl font-bold text-sky-800 mb-4">潜力与优势 (Upside)</h3>
                        <ul class="space-y-3 text-sm">
                            <li class="flex items-start"><span class="text-sky-500 mr-2">✓</span><div><strong class="font-semibold text-slate-700">顶级战略合作:</strong> 微软、NVIDIA、Chainlink加持，构建强大生态护城河。</div></li>
                            <li class="flex items-start"><span class="text-sky-500 mr-2">✓</span><div><strong class="font-semibold text-slate-700">革命性技术:</strong> “SQL证明”为Web3带来可验证计算，开辟新市场。</div></li>
                            <li class="flex items-start"><span class="text-sky-500 mr-2">✓</span><div><strong class="font-semibold text-slate-700">世界级团队:</strong> 汇集企业软件、数据科学与密码学顶尖专家。</div></li>
                            <li class="flex items-start"><span class="text-sky-500 mr-2">✓</span><div><strong class="font-semibold text-slate-700">明确的市场需求:</strong> 解决智能合约无法处理海量、可信数据的核心痛点。</div></li>
                        </ul>
                    </div>
                     <div class="card border-t-4 border-red-400">
                        <h3 class="text-xl font-bold text-red-800 mb-4">风险与挑战 (Downside)</h3>
                        <ul class="space-y-3 text-sm">
                            <li class="flex items-start"><span class="text-red-500 mr-2">✗</span><div><strong class="font-semibold text-slate-700">未解决的关键安全漏洞:</strong> 审计发现“潜在查询操纵风险”，动摇项目根基。</div></li>
                             <li class="flex items-start"><span class="text-red-500 mr-2">✗</span><div><strong class="font-semibold text-slate-700">团队透明度不足:</strong> 对关键安全问题保持沉默，损害投资者信任。</div></li>
                            <li class="flex items-start"><span class="text-red-500 mr-2">✗</span><div><strong class="font-semibold text-slate-700">2026年解锁悬崖:</strong> 团队与投资者代币大量解锁，或引发巨大抛压。</div></li>
                            <li class="flex items-start"><span class="text-red-500 mr-2">✗</span><div><strong class="font-semibold text-slate-700">质押锁仓风险:</strong> 当前标准质押的代币在下一次审计前无法解绑，流动性被锁定。</div></li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <section id="tech" class="pt-16 mb-16">
            <h2 class="section-title">核心技术：Web3的可验证计算层</h2>
            <p class="text-center max-w-3xl mx-auto mb-12 text-slate-600">Space and Time 不仅仅是一个数据仓库，它通过其核心创新“SQL证明”，让智能合约首次能够信任并使用链下计算的结果。以下是其工作流程的简化展示，点击各环节可查看详情。</p>
            <div id="tech-diagram" class="grid grid-cols-2 md:grid-cols-5 gap-4 text-center items-center text-sm font-semibold">
                <div class="tech-node p-4 border-2 border-slate-300 rounded-lg cursor-pointer hover:border-sky-500 hover:bg-sky-50 transition-colors" data-info="从各大主流区块链（如以太坊）及链下数据源（如企业数据库）抓取原始数据。">链上/链下数据</div>
                <div class="hidden md:block text-3xl text-slate-400">→</div>
                <div class="tech-node p-4 border-2 border-slate-300 rounded-lg cursor-pointer hover:border-sky-500 hover:bg-sky-50 transition-colors" data-info="索引者节点负责处理、格式化数据，并将其提交至SXT链，生成数据承诺。">索引者</div>
                <div class="col-span-2 md:hidden text-3xl text-slate-400 mx-auto transform rotate-90">↓</div>
                <div class="hidden md:block text-3xl text-slate-400">→</div>
                <div class="tech-node p-4 border-2 border-slate-300 rounded-lg cursor-pointer hover:border-sky-500 hover:bg-sky-50 transition-colors" data-info="证明者节点在链下执行SQL查询，并利用NVIDIA的GPU加速，为计算结果生成亚秒级的ZK证明。">证明者 & SQL证明</div>
                <div class="hidden md:block text-3xl text-slate-400">→</div>
                <div class="col-span-2 md:hidden text-3xl text-slate-400 mx-auto transform rotate-90">↓</div>
                 <div class="tech-node p-4 border-2 border-slate-300 rounded-lg cursor-pointer hover:border-sky-500 hover:bg-sky-50 transition-colors" data-info="验证者节点通过DPoS共识保护SXT链，并借助Chainlink网络将经过验证的数据和证明传递给智能合约。">验证者 & Chainlink</div>
                 <div class="hidden md:block text-3xl text-slate-400">→</div>
                 <div class="tech-node p-4 border-2 border-slate-300 rounded-lg cursor-pointer hover:border-sky-500 hover:bg-sky-50 transition-colors" data-info="智能合约接收到经过密码学验证的结果，从而可以执行更复杂、数据驱动的逻辑，例如DeFi衍生品、链上游戏或AI代理。">智能合约</div>
            </div>
            <div id="tech-info-box" class="mt-8 p-4 bg-slate-100 rounded-lg text-center text-slate-700 min-h-[60px] flex items-center justify-center">
                点击上方任意环节查看其作用。
            </div>
        </section>

        <section id="market" class="pt-16 mb-16">
            <h2 class="section-title">市场与竞争分析</h2>
            <p class="text-center max-w-3xl mx-auto mb-12 text-slate-600">SXT并非在真空中运作。它与The Graph (GRT)和Covalent (CQT)等数据服务项目存在竞争，但其核心差异在于“可验证计算”，这为其开辟了独特的市场定位。</p>
            <div class="grid md:grid-cols-2 gap-8">
                <div class="card">
                    <h3 class="font-bold text-lg text-center mb-4">竞品关键特性对比</h3>
                    <div class="chart-container h-96">
                        <canvas id="competitorChart"></canvas>
                    </div>
                     <p class="text-xs text-slate-400 text-center mt-2">注：此为基于报告分析的定性评估图表。</p>
                </div>
                <div class="card">
                    <h3 class="font-bold text-lg text-center mb-4">功能差异化速查</h3>
                     <table class="w-full text-sm text-left">
                        <thead class="bg-slate-50 text-slate-800">
                            <tr>
                                <th class="p-2">特性</th>
                                <th class="p-2 bg-sky-100 text-sky-900 font-bold">Space and Time (SXT)</th>
                                <th class="p-2">The Graph (GRT)</th>
                                <th class="p-2">Covalent (CQT)</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="border-b"><td class="p-2 font-semibold">核心功能</td><td class="p-2 bg-sky-50">可验证计算</td><td class="p-2">数据索引</td><td class="p-2">统一数据API</td></tr>
                            <tr class="border-b"><td class="p-2 font-semibold">查询语言</td><td class="p-2 bg-sky-50">SQL</td><td class="p-2">GraphQL</td><td class="p-2">REST API</td></tr>
                            <tr class="border-b"><td class="p-2 font-semibold">安全模型</td><td class="p-2 bg-sky-50">ZK证明</td><td class="p-2">经济博弈</td><td class="p-2">中心化</td></tr>
                             <tr class="border-b"><td class="p-2 font-semibold">目标用户</td><td class="p-2 bg-sky-50">企业 & DeFi</td><td class="p-2">Web3开发者</td><td class="p-2">Web3开发者</td></tr>
                             <tr><td class="p-2 font-semibold">链上验证</td><td class="p-2 bg-sky-50">原生支持</td><td class="p-2">不支持</td><td class="p-2">不适用</td></tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </section>

        <section id="tokenomics" class="pt-16 mb-16">
            <h2 class="section-title">代币经济学 (Tokenomics)</h2>
             <p class="text-center max-w-3xl mx-auto mb-12 text-slate-600">SXT代币是协议安全、支付和治理的核心。其分配结构旨在平衡多方利益，但解锁计划中包含一个需要重点关注的未来风险事件。</p>
            <div class="grid md:grid-cols-2 gap-8 items-center">
                <div class="card">
                    <h3 class="font-bold text-lg text-center mb-4">代币分配 (总供应量: 50亿)</h3>
                    <div class="chart-container">
                        <canvas id="tokenAllocationChart"></canvas>
                    </div>
                </div>
                <div class="card">
                     <h3 class="font-bold text-lg text-center mb-4">关键解锁时间线</h3>
                     <div class="relative pl-6">
                        <div class="absolute left-0 top-0 bottom-0 w-0.5 bg-slate-300"></div>
                        <div class="relative mb-8">
                            <div class="absolute left-[-29px] top-1/2 -translate-y-1/2 w-4 h-4 bg-sky-500 rounded-full border-4 border-white"></div>
                            <p class="font-semibold text-slate-800">2025年5月: TGE (代币生成)</p>
                            <p class="text-sm text-slate-500">社区、币安、做市商份额100%解锁。生态基金部分解锁。</p>
                        </div>
                        <div class="relative">
                             <div class="absolute left-[-29px] top-1/2 -translate-y-1/2 w-4 h-4 bg-red-500 rounded-full border-4 border-white animate-pulse"></div>
                            <p class="font-semibold text-red-700">2026年5月: 解锁悬崖</p>
                            <p class="text-sm text-slate-500">团队和投资者的代币结束12个月锁仓，约 <strong class="text-red-600">3.62亿</strong> SXT将首次变为流动，可能造成巨大抛售压力。</p>
                        </div>
                     </div>
                </div>
            </div>
        </section>
        
        <section id="risks" class="pt-16 mb-16">
            <h2 class="section-title">风险深度剖析</h2>
             <p class="text-center max-w-3xl mx-auto mb-12 text-slate-600">这是决定投资评级的核心部分。一个以“无需信任”为卖点的项目，其自身安全性存在的任何疑点都必须被置于最高优先级进行审视。</p>
             <div class="bg-red-50 border-l-4 border-red-500 text-red-800 p-6 rounded-r-lg shadow-lg card">
                 <h3 class="flex items-center text-2xl font-bold mb-2">
                     <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 mr-3" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.21 3.03-1.742 3.03H4.42c-1.532 0-2.492-1.696-1.742-3.03l5.58-9.92zM10 13a1 1 0 110-2 1 1 0 010 2zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" /></svg>
                     关键警示：未解决的安全漏洞
                 </h3>
                 <p class="font-semibold">Cyberscope的审计报告发现了一个“关键”(Critical)级别的未解决漏洞：</p>
                 <blockquote class="my-4 pl-4 border-l-2 border-red-400 font-mono text-lg">"Potential Query Manipulation Risk" (潜在的查询操纵风险)</blockquote>
                 <p class="mb-2"><strong class="font-semibold">影响分析：</strong> 此漏洞直接威胁到项目的核心价值主张。如果查询可以被操纵，那么“SQL证明”将毫无意义，整个平台的可信度将崩溃。这将对所有基于SxT的DeFi协议和dApp造成毁灭性打击。</p>
                 <p><strong class="font-semibold">治理风险：</strong> 截至报告撰写时，<strong class="underline decoration-red-500 decoration-wavy">Space and Time团队未对此关键漏洞进行任何公开回应或提供修复计划</strong>。这种缺乏透明度的行为，极大地增加了投资风险，并与项目宣扬的“无需信任”精神背道而驰。</p>
             </div>
        </section>

        <section id="strategies" class="pt-16">
            <h2 class="section-title">投资策略对比</h2>
            <p class="text-center max-w-3xl mx-auto mb-12 text-slate-600">基于以上分析，我们为不同类型的投资者梳理了潜在的参与策略。请注意，所有策略均受项目整体风险的影响。点击策略名称查看详情。</p>
            <div class="overflow-x-auto">
                <table class="w-full text-sm text-left shadow-md rounded-lg">
                    <thead class="bg-slate-100 text-slate-800 uppercase text-xs">
                        <tr>
                            <th class="p-3">投资策略</th>
                            <th class="p-3 text-center">所需技能</th>
                            <th class="p-3 text-center">潜在ROI (APR)</th>
                            <th class="p-3 text-center">风险等级</th>
                            <th class="p-3 text-center">锁仓期</th>
                        </tr>
                    </thead>
                    <tbody id="strategy-table">
                    </tbody>
                </table>
            </div>
            <div id="strategy-details" class="mt-6"></div>
        </section>

    </main>
    
    <footer class="text-center p-8 mt-16 border-t border-slate-200">
        <p class="text-sm text-slate-500">本应用内容根据公开信息生成，仅供研究参考，不构成任何投资建议。</p>
        <p class="text-xs text-slate-400 mt-1">数据截止日期：2025年6月下旬</p>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const mobileMenuButton = document.getElementById('mobile-menu-button');
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenuButton.addEventListener('click', () => {
                mobileMenu.classList.toggle('hidden');
            });

            const navLinks = document.querySelectorAll('.nav-link');
            navLinks.forEach(link => {
                link.addEventListener('click', () => {
                     mobileMenu.classList.add('hidden');
                });
            });

            const techNodes = document.querySelectorAll('.tech-node');
            const techInfoBox = document.getElementById('tech-info-box');
            techNodes.forEach(node => {
                node.addEventListener('click', () => {
                    techNodes.forEach(n => n.classList.remove('bg-sky-100', 'border-sky-500'));
                    node.classList.add('bg-sky-100', 'border-sky-500');
                    techInfoBox.textContent = node.dataset.info;
                });
            });

            const competitorData = {
                labels: ['安全保证 (ZK)', '可计算性', '企业友好度 (SQL)'],
                datasets: [
                    { label: 'Space and Time', data: [9, 8, 9], backgroundColor: 'rgba(56, 189, 248, 0.6)', borderColor: 'rgba(14, 165, 233, 1)', borderWidth: 1 },
                    { label: 'The Graph', data: [5, 3, 4], backgroundColor: 'rgba(167, 139, 250, 0.6)', borderColor: 'rgba(139, 92, 246, 1)', borderWidth: 1 },
                    { label: 'Covalent', data: [3, 2, 6], backgroundColor: 'rgba(113, 113, 122, 0.6)', borderColor: 'rgba(82, 82, 91, 1)', borderWidth: 1 }
                ]
            };
            const competitorChartCtx = document.getElementById('competitorChart').getContext('2d');
            new Chart(competitorChartCtx, {
                type: 'bar',
                data: competitorData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    indexAxis: 'y',
                    scales: { x: { beginAtZero: true, max: 10 } },
                    plugins: { legend: { position: 'bottom' } }
                }
            });

            const tokenAllocationData = {
                labels: ['生态与社区 (39.2%)', '投资者 (25.9%)', '团队 (22.4%)', '社区空投 (6%)', '币安用户 (4.5%)', '做市商 (2%)'],
                datasets: [{
                    data: [39.2, 25.9, 22.4, 6.0, 4.5, 2.0],
                    backgroundColor: ['#0ea5e9', '#6366f1', '#f97316', '#84cc16', '#facc15', '#a8a29e'],
                }]
            };
            const tokenAllocationChartCtx = document.getElementById('tokenAllocationChart').getContext('2d');
            new Chart(tokenAllocationChartCtx, {
                type: 'doughnut',
                data: tokenAllocationData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { display: false } }
                }
            });
            
            const strategies = [
                { name: '1. 直接购买SXT代币', skill: '低', roi: '价格增值', risk: '高', lock: '无', pros: ['流动性最高，进入门槛最低', '便于捕捉短期价格波动'], cons: ['完全暴露于市场和安全风险', '无被动收益']},
                { name: '2. 标准质押（委托人）', skill: '低', roi: '~7.9%', risk: '极高', lock: '未知', pros: ['操作简单，有被动收益'], cons: ['资金在下次审计前被锁定，无法解绑', '面临协议和市场双重风险']},
                { name: '3. 创世验证者计划', skill: '高', roi: '高达 ~30%', risk: '极高', lock: '9个月', pros: ['潜在APR最高'], cons: ['高技术和资本门槛', '9个月硬锁仓，风险巨大', '可能已过参与窗口']},
                { name: '4. 运行节点', skill: '很高', roi: '协议奖励', risk: '极高', lock: '协议规定', pros: ['直接参与网络，获取核心奖励'], cons: ['需要极高的技术专长和硬件投入']},
                { name: '5. 发布数据（生态参与）', skill: '中-高', roi: '市场决定', risk: '高', lock: '无', pros: ['利用自有数据资产创造收入'], cons: ['需要拥有有价值的链下数据', '收入不确定']}
            ];

            const strategyTable = document.getElementById('strategy-table');
            const strategyDetails = document.getElementById('strategy-details');

            const riskColors = { '高': 'bg-yellow-100 text-yellow-800', '极高': 'bg-red-100 text-red-800' };
            const skillColors = { '低': 'bg-green-100 text-green-800', '中-高': 'bg-yellow-100 text-yellow-800', '高': 'bg-orange-100 text-orange-800', '很高': 'bg-red-100 text-red-800'};

            strategies.forEach((s, index) => {
                const row = document.createElement('tr');
                row.className = 'border-b hover:bg-slate-50 cursor-pointer';
                row.dataset.index = index;
                row.innerHTML = `
                    <td class="p-3 font-semibold text-sky-700">${s.name}</td>
                    <td class="p-3 text-center"><span class="pill ${skillColors[s.skill]}">${s.skill}</span></td>
                    <td class="p-3 text-center font-semibold">${s.roi}</td>
                    <td class="p-3 text-center"><span class="pill ${riskColors[s.risk]}">${s.risk}</span></td>
                    <td class="p-3 text-center">${s.lock}</td>
                `;
                strategyTable.appendChild(row);
                
                row.addEventListener('click', () => {
                    const strategy = strategies[row.dataset.index];
                    document.querySelectorAll('#strategy-table tr').forEach(r => r.classList.remove('bg-sky-100'));
                    row.classList.add('bg-sky-100');

                    strategyDetails.innerHTML = `
                        <div class="card bg-slate-50">
                            <h4 class="font-bold text-lg mb-4 text-slate-800">${strategy.name} 详情</h4>
                            <div class="grid md:grid-cols-2 gap-4 text-sm">
                                <div>
                                    <h5 class="font-semibold text-green-700 mb-2">优点 (Pros)</h5>
                                    <ul class="list-disc list-inside space-y-1">
                                        ${strategy.pros.map(p => `<li>${p}</li>`).join('')}
                                    </ul>
                                </div>
                                <div>
                                    <h5 class="font-semibold text-red-700 mb-2">缺点 (Cons)</h5>
                                    <ul class="list-disc list-inside space-y-1">
                                        ${strategy.cons.map(c => `<li>${c}</li>`).join('')}
                                    </ul>
                                </div>
                            </div>
                        </div>
                    `;
                });
            });
            strategyTable.rows[0].click();
        });
    </script>
</body>
</html>
