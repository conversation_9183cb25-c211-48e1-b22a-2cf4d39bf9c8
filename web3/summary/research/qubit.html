<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Qubit Finance: 项目失败案例交互式分析</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!-- Chosen Palette: Forensic Neutral -->
    <!-- Application Structure Plan: The SPA is designed as a "digital autopsy" or "case file" to guide users through the evidence of Qubit Finance's failure. The structure is narrative-driven, not report-driven, to maximize impact and understanding. It flows from the final verdict to the supporting evidence: 1. **Header Verdict**: Immediately presents the "Strongly Do Not Recommend" conclusion. 2. **Vital Signs Dashboard**: Shows key metrics proving the project is dead. 3. **Timeline of Collapse**: An interactive vertical timeline to narrate the project's downfall. 4. **Anatomy of the Hack**: A visual flowchart explaining the technical exploit simply. 5. **Investment Traps**: An interactive table debunking any potential "opportunities". 6. **Evidence Locker**: A final section presenting concrete proof of abandonment (GitHub, social media). This structure is chosen to create a persuasive and memorable user experience that clearly communicates the extreme risk associated with the project. -->
    <!-- Visualization & Content Choices: 
        - Report Info: Final Rating -> Goal: Inform/Warn -> Presentation: Large, colored banner -> Interaction: Static -> Justification: Maximum immediate impact.
        - Report Info: Price/TVL/Volume data -> Goal: Inform/Compare -> Presentation: Dashboard-style cards -> Interaction: Static -> Justification: Quick, digestible summary of the project's dead state.
        - Report Info: Project history & hack event -> Goal: Show Change/Narrate -> Presentation: Interactive Timeline & Chart.js Line Chart -> Interaction: Click to expand details, hover on chart -> Justification: Transforms a dry history into an engaging story, visually correlating the hack with the collapse.
        - Report Info: Technical exploit details -> Goal: Organize/Explain -> Presentation: HTML/CSS Flowchart -> Interaction: Static -> Justification: Simplifies a complex process for non-technical users.
        - Report Info: Investment opportunity analysis -> Goal: Compare/Warn -> Presentation: Interactive HTML Table -> Interaction: Hover to highlight risks -> Justification: Directly addresses user intent and clearly marks all paths as dangerous.
        - Report Info: GitHub/Social Media status -> Goal: Inform/Verify -> Presentation: Evidence cards -> Interaction: Static -> Justification: Provides final, undeniable proof of abandonment.
        - Library/Method: Chart.js for visualizations. HTML/CSS/Unicode for diagrams. Vanilla JS for all interactions. -->
    <!-- CONFIRMATION: NO SVG graphics used. NO Mermaid JS used. -->
    <style>
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f8fafc; /* slate-50 */
        }
        .chart-container {
            position: relative;
            height: 40vh;
            width: 100%;
            max-width: 800px;
            margin: auto;
            max-height: 400px;
        }
        .timeline-item::before {
            content: '';
            position: absolute;
            left: -30px;
            top: 50%;
            transform: translateY(-50%);
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background-color: #cbd5e1; /* slate-300 */
            border: 4px solid #f8fafc; /* slate-50 */
        }
        .timeline-item.active::before {
            background-color: #ef4444; /* red-500 */
        }
        .timeline-line {
            position: absolute;
            left: -21px;
            top: 0;
            bottom: 0;
            width: 2px;
            background-color: #e2e8f0; /* slate-200 */
        }
    </style>
</head>
<body class="text-slate-700">

    <header class="bg-slate-800 text-white text-center py-8 px-4">
        <h1 class="text-3xl md:text-5xl font-bold mb-2">Qubit Finance (QBT)</h1>
        <p class="text-xl md:text-2xl text-slate-300">一个已消亡DeFi项目的数字尸检报告</p>
    </header>

    <main class="container mx-auto px-4 py-8 md:py-12">

        <!-- Section 1: The Verdict -->
        <section id="verdict" class="mb-12 md:mb-16">
            <div class="bg-red-600 text-white rounded-lg p-6 md:p-8 shadow-2xl text-center">
                <h2 class="text-2xl md:text-4xl font-bold mb-2">最终投资评级：强烈不建议</h2>
                <p class="text-base md:text-lg">该项目在遭受毁灭性的8000万美元黑客攻击后已被完全遗弃。任何形式的交互或投资都极有可能导致100%的资金损失。</p>
            </div>
        </section>

        <!-- Section 2: Vital Signs -->
        <section id="vitals" class="mb-12 md:mb-16">
            <h2 class="text-3xl font-bold text-center mb-8">生命体征：已无心跳</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="bg-white p-6 rounded-lg shadow-lg text-center border-t-4 border-red-500">
                    <h3 class="text-lg font-semibold text-slate-500">QBT 代币价格</h3>
                    <p class="text-4xl font-bold text-red-600 my-2">≈ $0.00016</p>
                    <p class="text-sm text-slate-500">较历史高点下跌 >99.9%</p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-lg text-center border-t-4 border-red-500">
                    <h3 class="text-lg font-semibold text-slate-500">24小时交易量</h3>
                    <p class="text-4xl font-bold text-red-600 my-2">$0</p>
                    <p class="text-sm text-slate-500">市场流动性完全枯竭</p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-lg text-center border-t-4 border-red-500">
                    <h3 class="text-lg font-semibold text-slate-500">总锁仓价值 (TVL)</h3>
                    <p class="text-4xl font-bold text-red-600 my-2">≈ $27k</p>
                    <p class="text-sm text-slate-500">信任与资本已完全清零</p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-lg text-center border-t-4 border-red-500">
                    <h3 class="text-lg font-semibold text-slate-500">平台状态</h3>
                    <p class="text-4xl font-bold text-red-600 my-2">已下线</p>
                    <p class="text-sm text-slate-500">官网及应用均无法访问</p>
                </div>
            </div>
        </section>

        <!-- Section 3: Timeline of Collapse -->
        <section id="timeline" class="mb-12 md:mb-16">
            <h2 class="text-3xl font-bold text-center mb-4">崩溃时间线</h2>
            <p class="text-center text-slate-600 max-w-3xl mx-auto mb-12">Qubit Finance的生命周期短暂而戏剧化。通过下面的时间线和图表，我们可以清晰地看到项目从启动到因一次致命黑客攻击而瞬间崩溃的全过程。这是一个关于创新、风险和最终失败的警示故事。</p>

            <div class="chart-container mb-12">
                <canvas id="collapseChart"></canvas>
            </div>

            <div class="relative max-w-3xl mx-auto pl-8">
                <div class="timeline-line"></div>
                <div id="timeline-container">
                    <!-- Timeline items will be injected by JavaScript -->
                </div>
            </div>
        </section>
        
        <!-- Section 4: Anatomy of the Hack -->
        <section id="hack" class="mb-12 md:mb-16">
            <h2 class="text-3xl font-bold text-center mb-4">致命漏洞：8000万美元大劫案剖析</h2>
            <p class="text-center text-slate-600 max-w-3xl mx-auto mb-12">项目的崩溃源于其核心跨链桥QBridge的一个逻辑漏洞。攻击者利用此缺陷，凭空铸造了无支持的抵押品，并提走了协议中所有真实资产。下面的流程图简化了整个攻击过程。</p>
            <div class="bg-white p-6 md:p-8 rounded-lg shadow-lg">
                <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0 md:space-x-4">
                    <div class="text-center p-4 border-2 border-slate-200 rounded-lg w-full md:w-1/4">
                        <h4 class="font-bold text-lg">1. 攻击发起</h4>
                        <p class="text-sm">攻击者在以太坊链上调用QBridge的<br><code class="bg-slate-100 text-red-600 px-1 rounded">deposit()</code>函数。</p>
                    </div>
                    <div class="text-2xl font-mono text-slate-400 transform md:rotate-0 rotate-90">&rarr;</div>
                    <div class="text-center p-4 border-2 border-red-400 border-dashed rounded-lg w-full md:w-1/4 bg-red-50">
                        <h4 class="font-bold text-lg text-red-700">2. 逻辑漏洞</h4>
                        <p class="text-sm text-red-700">合约未能验证实际存入的ETH数量，允许攻击者传入虚假存款数据。</p>
                    </div>
                    <div class="text-2xl font-mono text-slate-400 transform md:rotate-0 rotate-90">&rarr;</div>
                    <div class="text-center p-4 border-2 border-slate-200 rounded-lg w-full md:w-1/4">
                        <h4 class="font-bold text-lg">3. 凭空铸币</h4>
                        <p class="text-sm">在BNB链上，Qubit为攻击者铸造了大量无真实资产支持的qXETH代币。</p>
                    </div>
                     <div class="text-2xl font-mono text-slate-400 transform md:rotate-0 rotate-90">&rarr;</div>
                    <div class="text-center p-4 border-2 border-red-400 bg-red-50 rounded-lg w-full md:w-1/4">
                        <h4 class="font-bold text-lg text-red-700">4. 资产耗尽</h4>
                        <p class="text-sm text-red-700">攻击者使用虚假的qXETH作为抵押，借走并提光了池中所有价值8000万美元的真实资产。</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Section 5: Investment Traps -->
        <section id="traps" class="mb-12 md:mb-16">
            <h2 class="text-3xl font-bold text-center mb-4">投资机会分析：所有道路都通向陷阱</h2>
            <p class="text-center text-slate-600 max-w-3xl mx-auto mb-12">对于寻求机会的投资者，了解为何与Qubit相关的任何活动都极其危险至关重要。下表分析了所有可能的“投资”途径，并揭示了其背后的巨大风险。任何看似诱人的高收益率都是骗局的信号。</p>
            <div class="overflow-x-auto bg-white rounded-lg shadow-lg">
                <table class="min-w-full divide-y divide-slate-200">
                    <thead class="bg-slate-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">投资途径</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">关键风险与劣势</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">预期回报</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">结论</th>
                        </tr>
                    </thead>
                    <tbody id="investment-table" class="bg-white divide-y divide-slate-200">
                        <!-- Table rows will be injected by JavaScript -->
                    </tbody>
                </table>
            </div>
        </section>

        <!-- Section 6: Evidence Locker -->
        <section id="evidence">
            <h2 class="text-3xl font-bold text-center mb-8">证据档案：项目被遗弃的铁证</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h3 class="font-bold text-lg mb-2">1. 代码库静止</h3>
                    <p class="text-sm text-slate-600 mb-4">官方GitHub代码库的最后一次代码提交发生在2022年初，与黑客攻击时间点吻合。开发已完全停止。</p>
                    <a href="https://github.com/PancakeBunny-finance/qubit-finance" target="_blank" class="text-blue-600 hover:underline text-sm">查看GitHub &rarr;</a>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h3 class="font-bold text-lg mb-2">2. 社交媒体沉寂</h3>
                    <p class="text-sm text-slate-600 mb-4">官方推特账号 <code class="text-xs">@QubitFin</code> 自2022年初黑客事件后便停止更新。社区频道已无人管理。</p>
                    <a href="https://twitter.com/QubitFin" target="_blank" class="text-blue-600 hover:underline text-sm">查看Twitter &rarr;</a>
                </div>
                 <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h3 class="font-bold text-lg mb-2">3. 团队过往记录</h3>
                    <p class="text-sm text-slate-600 mb-4">Qubit由Mound团队开发，其另一个项目PancakeBunny同样遭受过灾难性的闪电贷攻击。这暴露了团队系统性的安全风险。</p>
                     <p class="text-slate-400 text-sm">无直接链接</p>
                </div>
            </div>
        </section>

    </main>

    <footer class="bg-slate-800 text-white text-center py-4 mt-12">
        <p class="text-sm text-slate-400">本报告基于公开信息生成，仅供参考，不构成财务建议。</p>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', () => {

            // Data for the application
            const timelineData = [
                { date: "2021年8月", event: "项目启动", description: "Qubit Finance作为PancakeBunny生态的借贷协议正式启动，旨在提供高效的借贷服务。", active: false },
                { date: "2021年9月", event: "价格顶峰", description: "QBT代币价格达到约$0.58的历史最高点，市场情绪高涨。", active: false },
                { date: "2022年1月27日", event: "灾难性黑客攻击", description: "攻击者利用QBridge漏洞，盗走价值约8000万美元的资产，协议价值瞬间蒸发。", active: true },
                { date: "2022年2月", event: "无效的应对", description: "团队公开恳求黑客归还资金并提出失败的补偿计划，但未能挽回损失或用户信心。", active: false },
                { date: "2022年至今", event: "项目遗弃", description: "团队停止所有开发和社区沟通，项目官网下线，社交媒体沉寂，Qubit Finance正式成为一个“僵尸协议”。", active: false }
            ];

            const investmentData = [
                {
                    path: '代币购买 (QBT)',
                    risks: '项目已失效并被遗弃。零效用、零流动性、未修复的漏洞、极高的完全损失风险。',
                    returns: '0% / 不适用',
                    conclusion: '陷阱'
                },
                {
                    path: '收益农耕 / LP',
                    risks: '平台无法运行，不存在任何资金池。任何声称提供Qubit挖矿的网站都是骗局。',
                    returns: '0% / 不适用',
                    conclusion: '骗局/陷阱'
                },
                {
                    path: '高APR策略组合',
                    risks: '此类策略需要一个功能正常、流动性充足且安全的底层协议。Qubit不具备任何这些条件。',
                    returns: '不适用',
                    conclusion: '不可能'
                },
                {
                    path: '生态系统参与 (开发)',
                    risks: '被遗弃的代码库，无团队、无支持、无路线图。与一个失败、被攻击的项目关联会损害声誉。',
                    returns: '不适用',
                    conclusion: '有害无益'
                }
            ];

            // Populate Timeline
            const timelineContainer = document.getElementById('timeline-container');
            timelineData.forEach(item => {
                const div = document.createElement('div');
                div.className = `timeline-item relative mb-8 pl-8 ${item.active ? 'active' : ''}`;
                div.innerHTML = `
                    <div class="cursor-pointer">
                        <p class="font-bold text-lg">${item.event}</p>
                        <p class="text-sm text-slate-500">${item.date}</p>
                        <div class="timeline-description hidden mt-2 text-slate-600 bg-white p-4 rounded-lg shadow">
                            ${item.description}
                        </div>
                    </div>
                `;
                timelineContainer.appendChild(div);

                div.querySelector('.cursor-pointer').addEventListener('click', () => {
                    const desc = div.querySelector('.timeline-description');
                    desc.classList.toggle('hidden');
                });
            });

            // Populate Investment Table
            const investmentTable = document.getElementById('investment-table');
            investmentData.forEach(item => {
                const tr = document.createElement('tr');
                tr.className = 'hover:bg-slate-50 transition-colors duration-200';
                tr.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-slate-900">${item.path}</td>
                    <td class="px-6 py-4 whitespace-normal text-sm text-slate-600">${item.risks}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-500">${item.returns}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-semibold">
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">${item.conclusion}</span>
                    </td>
                `;
                investmentTable.appendChild(tr);
            });
            
            // Render Chart
            const ctx = document.getElementById('collapseChart').getContext('2d');
            const collapseChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['2021-09', '2021-11', '2022-01-26', '2022-01-28', '2022-03', '2022-05'],
                    datasets: [
                        {
                            label: 'QBT 价格 (美元)',
                            data: [0.58, 0.3, 0.1, 0.008, 0.001, 0.0002],
                            borderColor: '#3b82f6', // blue-500
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            yAxisID: 'yPrice',
                            tension: 0.1,
                            fill: true,
                        },
                        {
                            label: 'TVL (百万美元)',
                            data: [300, 450, 400, 0.1, 0.05, 0.027],
                            borderColor: '#ef4444', // red-500
                            backgroundColor: 'rgba(239, 68, 68, 0.1)',
                            yAxisID: 'yTVL',
                            tension: 0.1,
                            fill: true,
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        mode: 'index',
                        intersect: false,
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: 'Qubit Finance 价格与TVL崩溃图 (黑客攻击前后)',
                            font: {
                                size: 16
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    let label = context.dataset.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    if (context.parsed.y !== null) {
                                        if (context.dataset.yAxisID === 'yPrice') {
                                            label += new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(context.parsed.y);
                                        } else {
                                             label += new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD', notation: 'compact' }).format(context.parsed.y * 1000000);
                                        }
                                    }
                                    return label;
                                }
                            }
                        },
                        annotation: {
                            annotations: {
                                line1: {
                                    type: 'line',
                                    xMin: '2022-01-27',
                                    xMax: '2022-01-27',
                                    borderColor: 'rgb(255, 99, 132)',
                                    borderWidth: 2,
                                    label: {
                                        content: "Hack",
                                        enabled: true,
                                        position: "start"
                                    }
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: '日期'
                            }
                        },
                        yPrice: {
                            type: 'logarithmic',
                            position: 'left',
                            title: {
                                display: true,
                                text: '价格 (对数刻度)'
                            },
                             ticks: {
                                callback: function(value, index, values) {
                                    return '$' + value.toExponential(1);
                                }
                            }
                        },
                        yTVL: {
                            type: 'logarithmic',
                            position: 'right',
                            title: {
                                display: true,
                                text: 'TVL (对数刻度)'
                            },
                             ticks: {
                                callback: function(value, index, values) {
                                    if (value >= 1000000) return (value / 1000000) + 'M';
                                    if (value >= 1000) return (value / 1000) + 'k';
                                    return '$' + value;
                                }
                            },
                            grid: {
                                drawOnChartArea: false, 
                            },
                        }
                    }
                }
            });

        });
    </script>

</body>
</html>
