import os
import re
import time
import argparse
from datetime import datetime, timedelta
import requests
import yaml
import asyncio
import aiohttp
from concurrent.futures import Thread<PERSON>oolExecutor
from collections import defaultdict
import json

# --- 配置 ---
VAULT_PATH = os.path.dirname(os.path.abspath(__file__))
HISTORY_PATH = os.path.join(VAULT_PATH, 'history')
DATA_PATH = os.path.join(VAULT_PATH, 'data')
PRICES_MD_PATH = os.path.join(DATA_PATH, 'token_prices.md')
COINGECKO_COIN_LIST_PATH = os.path.join(DATA_PATH, 'coingecko_coin_list.json')

def find_required_data():
    """扫描文件，提取所有独特的货币单位和日期。"""
    required = {}
    # New regex specifically finds `[date:: YYYYMMDD...` and captures the 8-digit date.
    # It correctly handles dates with or without trailing time information.
    date_pattern = re.compile(r'\[date::\s*(\d{8})')

    for root, _, files in os.walk(HISTORY_PATH):
        for file in files:
            if not file.endswith('.md'):
                continue
            
            unit = None
            dates = set()
            file_path = os.path.join(root, file)
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                    frontmatter_match = re.match(r'---\n(.*?)\n---', content, re.DOTALL)
                    if frontmatter_match:
                        frontmatter_str = frontmatter_match.group(1)
                        try:
                            frontmatter_data = yaml.safe_load(frontmatter_str)
                            if frontmatter_data and 'Unit' in frontmatter_data:
                                unit = str(frontmatter_data['Unit']).strip().upper()
                        except yaml.YAMLError:
                            pass
                    
                    date_matches = date_pattern.findall(content)
                    for date_str in date_matches: # date_str is 'YYYYMMDD'
                        formatted_date = f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:]}"
                        dates.add(formatted_date)
                
                if unit and dates:
                    if unit not in required:
                        required[unit] = set()
                    required[unit].update(dates)
            except Exception:
                pass
    return required

def load_existing_prices():
    """从现有的价格文件加载价格数据，仅支持frontmatter格式。"""
    prices = {}
    if not os.path.exists(PRICES_MD_PATH):
        return prices
    
    try:
        with open(PRICES_MD_PATH, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 解析frontmatter
        frontmatter_match = re.match(r'---\n(.*?)\n---', content, re.DOTALL)
        if frontmatter_match:
            frontmatter_str = frontmatter_match.group(1)
            try:
                frontmatter_data = yaml.safe_load(frontmatter_str)
                if frontmatter_data and 'prices' in frontmatter_data:
                    # 转换数据格式以保持兼容性，确保日期键是字符串
                    for token, date_prices in frontmatter_data['prices'].items():
                        if isinstance(date_prices, dict):
                            prices[token.upper()] = {}
                            for date, price in date_prices.items():
                                # 确保日期是字符串格式
                                date_str = str(date)
                                if 'T' in date_str:
                                    date_str = date_str.split('T')[0]  # 只保留日期部分
                                prices[token.upper()][date_str] = float(price)
                    print(f"✅ 已加载 {len(prices)} 个代币的价格数据（frontmatter格式）")
                    return prices
            except yaml.YAMLError as e:
                print(f"警告：YAML解析错误: {e}")
                
    except (IOError, Exception) as e:
        print(f"警告：加载现有价格数据时出错: {e}")
        
    return prices

def get_coingecko_coin_list():
    """获取CoinGecko的代币列表并缓存到本地。"""
    if os.path.exists(COINGECKO_COIN_LIST_PATH):
        try:
            with open(COINGECKO_COIN_LIST_PATH, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (json.JSONDecodeError, IOError):
            pass

    print("正在从 CoinGecko 下载代币列表...")
    try:
        response = requests.get('https://api.coingecko.com/api/v3/coins/list', timeout=10)
        response.raise_for_status()
        coin_list = response.json()
        with open(COINGECKO_COIN_LIST_PATH, 'w', encoding='utf-8') as f:
            json.dump(coin_list, f)
        return coin_list
    except requests.exceptions.RequestException as e:
        print(f"错误: 无法从 CoinGecko 下载代币列表: {e}")
        return None

class RateLimiter:
    """智能速率限制器"""
    def __init__(self):
        self.api_limits = {
            'binance': {'calls_per_minute': 1200, 'last_calls': []},
            'coingecko': {'calls_per_minute': 50, 'last_calls': []},
            'cryptocompare': {'calls_per_minute': 100, 'last_calls': []},
            'coinapi': {'calls_per_minute': 100, 'last_calls': []},
            'bitget': {'calls_per_minute': 600, 'last_calls': []},
            'bybit': {'calls_per_minute': 600, 'last_calls': []},
            'gateio': {'calls_per_minute': 300, 'last_calls': []},
            'okx': {'calls_per_minute': 600, 'last_calls': []},
            'kucoin': {'calls_per_minute': 400, 'last_calls': []}
        }
    
    async def wait_if_needed(self, api_name):
        """根据实际使用情况智能等待"""
        if api_name not in self.api_limits:
            return
            
        limits = self.api_limits[api_name]
        now = time.time()
        
        # 移除1分钟前的调用记录
        limits['last_calls'] = [
            call_time for call_time in limits['last_calls'] 
            if now - call_time < 60
        ]
        
        # 如果达到限制，等待到最早的调用满1分钟
        if len(limits['last_calls']) >= limits['calls_per_minute']:
            wait_time = 60 - (now - limits['last_calls'][0])
            if wait_time > 0:
                await asyncio.sleep(wait_time)
                # 清理过期记录
                now = time.time()
                limits['last_calls'] = [
                    call_time for call_time in limits['last_calls'] 
                    if now - call_time < 60
                ]
        
        limits['last_calls'].append(now)

class APIClient:
    """异步API客户端，支持连接池和会话管理"""
    def __init__(self):
        self.sessions = {}
        self.rate_limiter = RateLimiter()
    
    async def get_session(self, base_url):
        if base_url not in self.sessions:
            connector = aiohttp.TCPConnector(
                limit=100,  # 总连接池大小
                limit_per_host=10,  # 每个主机的连接限制
                keepalive_timeout=300
            )
            self.sessions[base_url] = aiohttp.ClientSession(
                connector=connector,
                timeout=aiohttp.ClientTimeout(total=15)
            )
        return self.sessions[base_url]
    
    async def close_all_sessions(self):
        """关闭所有会话"""
        for session in self.sessions.values():
            await session.close()
        self.sessions.clear()

# 全局API客户端实例
api_client = APIClient()

async def get_historical_price_from_binance_async(unit, date_str):
    """从币安获取指定日期的收盘价（异步版本）。"""
    symbol = f"{unit}USDT"
    dt = datetime.strptime(date_str, '%Y-%m-%d')
    start_time = int(dt.timestamp() * 1000)
    
    api_url = "https://api.binance.com/api/v3/klines"
    params = {'symbol': symbol, 'interval': '1d', 'startTime': start_time, 'limit': 1}
    
    try:
        await api_client.rate_limiter.wait_if_needed('binance')
        session = await api_client.get_session('https://api.binance.com')
        
        async with session.get(api_url, params=params) as response:
            if response.status == 400:
                print(f"   ❌ Binance: 交易对 {symbol} 不存在")
                return None
            elif response.status == 429:
                print(f"   ❌ Binance: API请求频率限制 (429)")
                return None
            elif response.status != 200:
                print(f"   ❌ Binance: HTTP错误 {response.status}")
                return None
                
            data = await response.json()
            if data:
                price = float(data[0][4])
                print(f"   ✅ Binance: ${price}")
                return price
            else:
                print(f"   ❌ Binance: 返回数据为空")
                return None
    except asyncio.TimeoutError:
        print(f"   ❌ Binance: 请求超时")
        return None
    except aiohttp.ClientError:
        print(f"   ❌ Binance: 网络连接错误")
        return None
    except Exception as e:
        print(f"   ❌ Binance: 未知错误 - {str(e)}")
        return None

async def get_historical_price_from_coingecko_async(unit, date_str, coin_list):
    """从CoinGecko获取指定日期的价格（异步版本）。"""
    coin_id = next((coin['id'] for coin in coin_list if coin['symbol'].upper() == unit), None)
    if not coin_id:
        print(f"   ❌ CoinGecko: 代币 {unit} 在列表中未找到")
        return None
        
    dt = datetime.strptime(date_str, '%Y-%m-%d')
    date_param = dt.strftime('%d-%m-%Y')
    
    api_url = f"https://api.coingecko.com/api/v3/coins/{coin_id}/history"
    params = {'date': date_param}
    
    try:
        await api_client.rate_limiter.wait_if_needed('coingecko')
        session = await api_client.get_session('https://api.coingecko.com')
        
        async with session.get(api_url, params=params) as response:
            if response.status == 429:
                print(f"   ❌ CoinGecko: API请求频率限制 (429)")
                return None
            elif response.status == 404:
                print(f"   ❌ CoinGecko: 代币 {coin_id} 在指定日期无数据")
                return None
            elif response.status != 200:
                print(f"   ❌ CoinGecko: HTTP错误 {response.status}")
                return None
                
            data = await response.json()
            
            if 'market_data' in data and 'current_price' in data['market_data'] and 'usd' in data['market_data']['current_price']:
                price = data['market_data']['current_price']['usd']
                print(f"   ✅ CoinGecko: ${price}")
                return price
            else:
                print(f"   ❌ CoinGecko: 返回数据中缺少价格字段")
                return None
    except asyncio.TimeoutError:
        print(f"   ❌ CoinGecko: 请求超时")
        return None
    except aiohttp.ClientError:
        print(f"   ❌ CoinGecko: 网络连接错误")
        return None
    except Exception as e:
        print(f"   ❌ CoinGecko: 未知错误 - {str(e)}")
        return None

async def get_historical_price_from_bybit_async(unit, date_str):
    """从Bybit获取指定日期的价格（异步版本）。"""
    symbol = f"{unit}USDT"
    dt = datetime.strptime(date_str, '%Y-%m-%d')
    start_time = int(dt.timestamp() * 1000)
    
    api_url = "https://api.bybit.com/v5/market/kline"
    params = {
        'category': 'spot',
        'symbol': symbol,
        'interval': 'D',
        'start': start_time,
        'limit': 1
    }
    
    try:
        await api_client.rate_limiter.wait_if_needed('bybit')
        session = await api_client.get_session('https://api.bybit.com')
        
        async with session.get(api_url, params=params) as response:
            if response.status == 400:
                print(f"   ❌ Bybit: 交易对 {symbol} 不存在")
                return None
            elif response.status == 429:
                print(f"   ❌ Bybit: API请求频率限制 (429)")
                return None
            elif response.status != 200:
                print(f"   ❌ Bybit: HTTP错误 {response.status}")
                return None
                
            data = await response.json()
            
            if data.get('retCode') == 0 and data.get('result', {}).get('list'):
                kline_data = data['result']['list'][0]
                close_price = float(kline_data[4])
                print(f"   ✅ Bybit: ${close_price}")
                return close_price
            else:
                print(f"   ❌ Bybit: 无数据或API错误 - {data.get('retMsg', 'Unknown')}")
                return None
    except asyncio.TimeoutError:
        print(f"   ❌ Bybit: 请求超时")
        return None
    except aiohttp.ClientError:
        print(f"   ❌ Bybit: 网络连接错误")
        return None
    except Exception as e:
        print(f"   ❌ Bybit: 未知错误 - {str(e)}")
        return None

def get_historical_price_from_cryptocompare(unit, date_str):
    """从CryptoCompare获取指定日期的价格。"""
    dt = datetime.strptime(date_str, '%Y-%m-%d')
    timestamp = int(dt.timestamp())
    
    api_url = "https://min-api.cryptocompare.com/data/pricehistorical"
    params = {'fsym': unit, 'tsyms': 'USD', 'ts': timestamp}
    try:
        time.sleep(0.3)
        response = requests.get(api_url, params=params, timeout=15)
        
        if response.status_code == 429:
            print(f"   ❌ CryptoCompare: API请求频率限制 (429)")
            return None
        elif response.status_code != 200:
            print(f"   ❌ CryptoCompare: HTTP错误 {response.status_code}")
            return None
            
        data = response.json()
        
        if unit in data and 'USD' in data[unit]:
            price = data[unit]['USD']
            if price > 0:
                print(f"   ✅ CryptoCompare: ${price}")
                return price
            else:
                print(f"   ❌ CryptoCompare: 返回价格为0")
                return None
        else:
            print(f"   ❌ CryptoCompare: 代币 {unit} 未找到")
            return None
    except requests.exceptions.Timeout:
        print(f"   ❌ CryptoCompare: 请求超时 (15秒)")
        return None
    except requests.exceptions.ConnectionError:
        print(f"   ❌ CryptoCompare: 网络连接错误")
        return None
    except requests.exceptions.RequestException as e:
        print(f"   ❌ CryptoCompare: 请求异常 - {str(e)}")
        return None
    except Exception as e:
        print(f"   ❌ CryptoCompare: 未知错误 - {str(e)}")
        return None

def get_historical_price_from_coinapi(unit, date_str):
    """从CoinAPI获取指定日期的价格（需要免费API key）。"""
    # 注意：CoinAPI需要注册获取免费API key，这里提供框架
    api_key = "27feed42-d134-4a22-b648-fafee0f8763e"  # 需要替换为实际的API key
    if api_key == "YOUR_COINAPI_KEY":
        print(f"   ⚠️  CoinAPI: 需要配置API key，跳过")
        return None
        
    dt = datetime.strptime(date_str, '%Y-%m-%d')
    date_param = dt.strftime('%Y-%m-%dT00:00:00')
    
    api_url = f"https://rest.coinapi.io/v1/exchangerate/{unit}/USD"
    headers = {'X-CoinAPI-Key': api_key}
    params = {'time': date_param}
    
    try:
        time.sleep(0.1)
        response = requests.get(api_url, headers=headers, params=params, timeout=15)
        
        if response.status_code == 429:
            print(f"   ❌ CoinAPI: API请求频率限制 (429)")
            return None
        elif response.status_code == 401:
            print(f"   ❌ CoinAPI: API key无效")
            return None
        elif response.status_code != 200:
            print(f"   ❌ CoinAPI: HTTP错误 {response.status_code}")
            return None
            
        data = response.json()
        
        if 'rate' in data:
            price = data['rate']
            print(f"   ✅ CoinAPI: ${price}")
            return price
        else:
            print(f"   ❌ CoinAPI: 返回数据中缺少rate字段")
            return None
    except requests.exceptions.Timeout:
        print(f"   ❌ CoinAPI: 请求超时 (15秒)")
        return None
    except requests.exceptions.ConnectionError:
        print(f"   ❌ CoinAPI: 网络连接错误")
        return None
    except requests.exceptions.RequestException as e:
        print(f"   ❌ CoinAPI: 请求异常 - {str(e)}")
        return None
    except Exception as e:
        print(f"   ❌ CoinAPI: 未知错误 - {str(e)}")
        return None

def get_historical_price_from_bitget(unit, date_str):
    """从Bitget获取指定日期的价格。"""
    symbol = f"{unit}USDT"
    dt = datetime.strptime(date_str, '%Y-%m-%d')
    start_time = int(dt.timestamp() * 1000)
    end_time = start_time + 24 * 60 * 60 * 1000  # 加24小时
    
    api_url = "https://api.bitget.com/api/v2/spot/market/candles"
    params = {
        'symbol': symbol,
        'granularity': '1day',
        'startTime': start_time,
        'endTime': end_time,
        'limit': 1
    }
    
    try:
        time.sleep(0.2)
        response = requests.get(api_url, params=params, timeout=15)
        
        if response.status_code == 400:
            print(f"   ❌ Bitget: 交易对 {symbol} 不存在")
            return None
        elif response.status_code == 429:
            print(f"   ❌ Bitget: API请求频率限制 (429)")
            return None
        elif response.status_code != 200:
            print(f"   ❌ Bitget: HTTP错误 {response.status_code}")
            return None
            
        data = response.json()
        
        if data.get('code') == '00000' and data.get('data') and len(data['data']) > 0:
            # Bitget返回格式: [timestamp, open, high, low, close, volume]
            close_price = float(data['data'][0][4])
            print(f"   ✅ Bitget: ${close_price}")
            return close_price
        else:
            print(f"   ❌ Bitget: 无数据或API错误 - {data.get('msg', 'Unknown')}")
            return None
    except requests.exceptions.Timeout:
        print(f"   ❌ Bitget: 请求超时 (15秒)")
        return None
    except requests.exceptions.ConnectionError:
        print(f"   ❌ Bitget: 网络连接错误")
        return None
    except requests.exceptions.RequestException as e:
        print(f"   ❌ Bitget: 请求异常 - {str(e)}")
        return None
    except Exception as e:
        print(f"   ❌ Bitget: 未知错误 - {str(e)}")
        return None

def get_historical_price_from_bybit(unit, date_str):
    """从Bybit获取指定日期的价格。"""
    symbol = f"{unit}USDT"
    dt = datetime.strptime(date_str, '%Y-%m-%d')
    start_time = int(dt.timestamp() * 1000)
    
    api_url = "https://api.bybit.com/v5/market/kline"
    params = {
        'category': 'spot',
        'symbol': symbol,
        'interval': 'D',
        'start': start_time,
        'limit': 1
    }
    
    try:
        time.sleep(0.2)
        response = requests.get(api_url, params=params, timeout=15)
        
        if response.status_code == 400:
            print(f"   ❌ Bybit: 交易对 {symbol} 不存在")
            return None
        elif response.status_code == 429:
            print(f"   ❌ Bybit: API请求频率限制 (429)")
            return None
        elif response.status_code != 200:
            print(f"   ❌ Bybit: HTTP错误 {response.status_code}")
            return None
            
        data = response.json()
        
        if data.get('retCode') == 0 and data.get('result', {}).get('list'):
            # Bybit返回格式: [startTime, open, high, low, close, volume, turnover]
            kline_data = data['result']['list'][0]
            close_price = float(kline_data[4])
            print(f"   ✅ Bybit: ${close_price}")
            return close_price
        else:
            print(f"   ❌ Bybit: 无数据或API错误 - {data.get('retMsg', 'Unknown')}")
            return None
    except requests.exceptions.Timeout:
        print(f"   ❌ Bybit: 请求超时 (15秒)")
        return None
    except requests.exceptions.ConnectionError:
        print(f"   ❌ Bybit: 网络连接错误")
        return None
    except requests.exceptions.RequestException as e:
        print(f"   ❌ Bybit: 请求异常 - {str(e)}")
        return None
    except Exception as e:
        print(f"   ❌ Bybit: 未知错误 - {str(e)}")
        return None

def get_historical_price_from_gateio(unit, date_str):
    """从Gate.io获取指定日期的价格。"""
    symbol = f"{unit}_USDT"
    dt = datetime.strptime(date_str, '%Y-%m-%d')
    start_time = int(dt.timestamp())
    
    api_url = "https://api.gateio.ws/api/v4/spot/candlesticks"
    params = {
        'currency_pair': symbol,
        'interval': '1d',
        'from': start_time,
        'limit': 1
    }
    
    try:
        time.sleep(0.2)
        response = requests.get(api_url, params=params, timeout=15)
        
        if response.status_code == 400:
            print(f"   ❌ Gate.io: 交易对 {symbol} 不存在")
            return None
        elif response.status_code == 429:
            print(f"   ❌ Gate.io: API请求频率限制 (429)")
            return None
        elif response.status_code != 200:
            print(f"   ❌ Gate.io: HTTP错误 {response.status_code}")
            return None
            
        data = response.json()
        
        if isinstance(data, list) and len(data) > 0:
            # Gate.io返回格式: [timestamp, volume, close, high, low, open]
            close_price = float(data[0][2])
            print(f"   ✅ Gate.io: ${close_price}")
            return close_price
        else:
            print(f"   ❌ Gate.io: 无数据返回")
            return None
    except requests.exceptions.Timeout:
        print(f"   ❌ Gate.io: 请求超时 (15秒)")
        return None
    except requests.exceptions.ConnectionError:
        print(f"   ❌ Gate.io: 网络连接错误")
        return None
    except requests.exceptions.RequestException as e:
        print(f"   ❌ Gate.io: 请求异常 - {str(e)}")
        return None
    except Exception as e:
        print(f"   ❌ Gate.io: 未知错误 - {str(e)}")
        return None

def get_historical_price_from_okx(unit, date_str):
    """从OKX获取指定日期的价格。"""
    symbol = f"{unit}-USDT"
    dt = datetime.strptime(date_str, '%Y-%m-%d')
    start_time = int(dt.timestamp() * 1000)
    
    api_url = "https://www.okx.com/api/v5/market/history-candles"
    params = {
        'instId': symbol,
        'bar': '1D',
        'before': start_time,
        'limit': 1
    }
    
    try:
        time.sleep(0.2)
        response = requests.get(api_url, params=params, timeout=15)
        
        if response.status_code == 400:
            print(f"   ❌ OKX: 交易对 {symbol} 不存在")
            return None
        elif response.status_code == 429:
            print(f"   ❌ OKX: API请求频率限制 (429)")
            return None
        elif response.status_code != 200:
            print(f"   ❌ OKX: HTTP错误 {response.status_code}")
            return None
            
        data = response.json()
        
        if data.get('code') == '0' and data.get('data') and len(data['data']) > 0:
            # OKX返回格式: [timestamp, open, high, low, close, volume, volCcy]
            close_price = float(data['data'][0][4])
            print(f"   ✅ OKX: ${close_price}")
            return close_price
        else:
            print(f"   ❌ OKX: 无数据或API错误 - {data.get('msg', 'Unknown')}")
            return None
    except requests.exceptions.Timeout:
        print(f"   ❌ OKX: 请求超时 (15秒)")
        return None
    except requests.exceptions.ConnectionError:
        print(f"   ❌ OKX: 网络连接错误")
        return None
    except requests.exceptions.RequestException as e:
        print(f"   ❌ OKX: 请求异常 - {str(e)}")
        return None
    except Exception as e:
        print(f"   ❌ OKX: 未知错误 - {str(e)}")
        return None

def get_historical_price_from_kucoin(unit, date_str):
    """从KuCoin获取指定日期的价格。"""
    symbol = f"{unit}-USDT"
    dt = datetime.strptime(date_str, '%Y-%m-%d')
    start_time = int(dt.timestamp())
    end_time = start_time + 24 * 60 * 60  # 加24小时
    
    api_url = "https://api.kucoin.com/api/v1/market/candles"
    params = {
        'symbol': symbol,
        'type': '1day',
        'startAt': start_time,
        'endAt': end_time
    }
    
    try:
        time.sleep(0.2)
        response = requests.get(api_url, params=params, timeout=15)
        
        if response.status_code == 400:
            print(f"   ❌ KuCoin: 交易对 {symbol} 不存在")
            return None
        elif response.status_code == 429:
            print(f"   ❌ KuCoin: API请求频率限制 (429)")
            return None
        elif response.status_code != 200:
            print(f"   ❌ KuCoin: HTTP错误 {response.status_code}")
            return None
            
        data = response.json()
        
        if data.get('code') == '200000' and data.get('data') and len(data['data']) > 0:
            # KuCoin返回格式: [timestamp, open, close, high, low, volume, turnover]
            close_price = float(data['data'][0][2])
            print(f"   ✅ KuCoin: ${close_price}")
            return close_price
        else:
            print(f"   ❌ KuCoin: 无数据或API错误 - {data.get('msg', 'Unknown')}")
            return None
    except requests.exceptions.Timeout:
        print(f"   ❌ KuCoin: 请求超时 (15秒)")
        return None
    except requests.exceptions.ConnectionError:
        print(f"   ❌ KuCoin: 网络连接错误")
        return None
    except requests.exceptions.RequestException as e:
        print(f"   ❌ KuCoin: 请求异常 - {str(e)}")
        return None
    except Exception as e:
        print(f"   ❌ KuCoin: 未知错误 - {str(e)}")
        return None

async def get_price_for_token_date_async(unit, date_str, coin_list, is_realtime=False):
    """异步获取单个代币在指定日期的价格"""
    if is_realtime:
        print(f"获取实时价格: {unit}...")
        # 实时价格获取逻辑（保持同步，因为这部分比较简单）
        price = get_current_price_from_binance(unit)
        if price is None:
            price = get_current_price_from_coingecko(unit, coin_list)
        return price
    else:
        print(f"🔍 获取历史价格: {unit} 在 {date_str}...")
        
        # 定义异步API源的优先级
        async_sources_tier1 = [
            ("Binance", lambda u, d: get_historical_price_from_binance_async(u, d)),
            ("Bybit", lambda u, d: get_historical_price_from_bybit_async(u, d)),
        ]
        
        async_sources_tier2 = [
            ("CoinGecko", lambda u, d: get_historical_price_from_coingecko_async(u, d, coin_list)),
        ]
        
        # 尝试第1层API
        for source_name, source_func in async_sources_tier1:
            try:
                price = await source_func(unit, date_str)
                if price is not None:
                    return price
            except Exception as e:
                print(f"   ❌ {source_name}: 异常 - {str(e)}")
        
        # 尝试第2层API
        for source_name, source_func in async_sources_tier2:
            try:
                print(f"   🔄 尝试 {source_name}...")
                price = await source_func(unit, date_str)
                if price is not None:
                    return price
            except Exception as e:
                print(f"   ❌ {source_name}: 异常 - {str(e)}")
        
        # 如果异步源都失败，回退到同步源
        print(f"   🔄 尝试同步API作为后备...")
        
        # 第3层: 其他CEX交易所（同步）
        sync_sources_tier3 = [
            ("OKX", get_historical_price_from_okx),
            ("Bitget", get_historical_price_from_bitget),
            ("Gate.io", get_historical_price_from_gateio),
            ("KuCoin", get_historical_price_from_kucoin),
        ]
        
        for source_name, source_func in sync_sources_tier3:
            try:
                print(f"   🔄 尝试 {source_name}...")
                price = source_func(unit, date_str)
                if price is not None:
                    return price
            except Exception as e:
                print(f"   ❌ {source_name}: 异常 - {str(e)}")
        
        # 第4层: 数据聚合商（同步）
        sync_sources_tier4 = [
            ("CryptoCompare", get_historical_price_from_cryptocompare),
            ("CoinAPI", get_historical_price_from_coinapi),
        ]
        
        for source_name, source_func in sync_sources_tier4:
            try:
                print(f"   🔄 尝试 {source_name}...")
                price = source_func(unit, date_str)
                if price is not None:
                    return price
            except Exception as e:
                print(f"   ❌ {source_name}: 异常 - {str(e)}")
        
        return None

async def process_token_parallel(unit, dates, existing_prices, coin_list, is_realtime=False):
    """并行处理单个代币的所有日期"""
    stablecoins = ['USDT', 'USDC', 'DAI']
    if unit in stablecoins:
        return False
        
    updated = False
    today = datetime.now().strftime('%Y-%m-%d')
    
    if unit not in existing_prices:
        existing_prices[unit] = {}
    
    # 收集需要处理的日期
    dates_to_process = []
    for date_str in dates:
        if is_realtime:
            date_str = today
            
        needs_update = False
        if is_realtime:
            needs_update = (date_str == today)
        else:
            needs_update = date_str not in existing_prices[unit]
            if not needs_update:
                print(f"⏭️  跳过: {unit} 在 {date_str} (历史价格已存在)")
            
        if needs_update:
            dates_to_process.append(date_str)
            
        if is_realtime:
            break
    
    if not dates_to_process:
        print(f"ℹ️  {unit}: 所有价格已存在，无需更新")
        return False
    
    # 限制并发数量以避免API速率限制
    semaphore = asyncio.Semaphore(2)  # 每个代币最多2个并发请求
    
    async def process_single_date(date_str):
        async with semaphore:
            price = await get_price_for_token_date_async(unit, date_str, coin_list, is_realtime)
            if price is not None:
                existing_prices[unit][date_str] = price
                mode = "实时" if is_realtime else f"在 {date_str}"
                print(f"✅ 价格已添加: {unit} {mode} = ${price}")
                return True
            else:
                mode = "实时" if is_realtime else f"在 {date_str}"
                print(f"❌ 警告: 未能从任何来源找到 {unit} 的{mode}价格。")
                return False
    
    # 并行处理所有日期
    results = await asyncio.gather(*[process_single_date(date) for date in dates_to_process], return_exceptions=True)
    
    # 检查是否有任何成功的更新
    for result in results:
        if isinstance(result, bool) and result:
            updated = True
        elif isinstance(result, Exception):
            print(f"   ❌ 处理 {unit} 时发生异常: {result}")
    
    return updated

def get_current_price_from_binance(unit):
    """从币安获取当前实时价格。"""
    symbol = f"{unit}USDT"
    api_url = "https://api.binance.com/api/v3/ticker/price"
    params = {'symbol': symbol}
    try:
        time.sleep(0.1)
        response = requests.get(api_url, params=params, timeout=10)
        if response.status_code == 400:
            return None # 交易对不存在
        response.raise_for_status()
        data = response.json()
        if 'price' in data:
            return float(data['price'])
        return None
    except requests.exceptions.RequestException:
        return None

def get_current_price_from_coingecko(unit, coin_list):
    """从CoinGecko获取当前实时价格。"""
    coin_id = next((coin['id'] for coin in coin_list if coin['symbol'].upper() == unit), None)
    if not coin_id:
        return None
        
    api_url = f"https://api.coingecko.com/api/v3/simple/price"
    params = {'ids': coin_id, 'vs_currencies': 'usd'}
    try:
        time.sleep(0.3)
        response = requests.get(api_url, params=params, timeout=10)
        response.raise_for_status()
        data = response.json()
        if coin_id in data and 'usd' in data[coin_id]:
            return data[coin_id]['usd']
        return None
    except requests.exceptions.RequestException:
        return None

async def update_price_data_async(required_data, existing_prices, coin_list, is_realtime=False, max_concurrent_tokens=3):
    """并行更新价格数据"""
    print(f"🚀 开始并行处理 {len(required_data)} 个代币，最大并发数: {max_concurrent_tokens}")
    
    # 创建代币处理任务
    semaphore = asyncio.Semaphore(max_concurrent_tokens)
    
    async def process_token_with_semaphore(unit, dates):
        async with semaphore:
            return await process_token_parallel(unit, dates, existing_prices, coin_list, is_realtime)
    
    # 启动所有代币的并行处理
    tasks = [
        process_token_with_semaphore(unit, dates) 
        for unit, dates in required_data.items()
    ]
    
    try:
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 统计结果
        updated_count = 0
        error_count = 0
        for i, result in enumerate(results):
            if isinstance(result, bool) and result:
                updated_count += 1
            elif isinstance(result, Exception):
                error_count += 1
                unit = list(required_data.keys())[i]
                print(f"❌ 处理代币 {unit} 时发生错误: {result}")
        
        print(f"✅ 并行处理完成: {updated_count} 个代币已更新, {error_count} 个错误")
        return updated_count > 0
        
    except Exception as e:
        print(f"❌ 并行处理过程中发生严重错误: {e}")
        return False
    finally:
        # 清理异步会话
        await api_client.close_all_sessions()

def update_price_data(required_data, existing_prices, coin_list, is_realtime=False):
    """检查并更新价格数据。"""
    stablecoins = ['USDT', 'USDC', 'DAI']
    updated = False
    today = datetime.now().strftime('%Y-%m-%d')
    
    for unit, dates in required_data.items():
        if unit in stablecoins:
            continue
        
        if unit not in existing_prices:
            existing_prices[unit] = {}
            
        for date_str in dates:
            # 如果是实时模式，使用今天的日期
            if is_realtime:
                date_str = today
                
            # 检查是否需要更新的逻辑
            if is_realtime:
                # 实时模式：只有当天的价格需要每次更新
                needs_update = (date_str == today)
                update_reason = "实时价格更新"
            else:
                # 历史模式：只有不存在的历史价格才需要获取（历史数据不可变）
                needs_update = date_str not in existing_prices[unit]
                update_reason = "历史价格缺失" if needs_update else "历史价格已存在"
            
            if needs_update:
                if is_realtime:
                    print(f"获取实时价格: {unit} ({update_reason})...")
                    price = get_current_price_from_binance(unit)
                    if price is None:
                        print(f"在币安未找到 {unit}，尝试从 CoinGecko 获取...")
                        price = get_current_price_from_coingecko(unit, coin_list)
                else:
                    print(f"🔍 获取历史价格: {unit} 在 {date_str} ({update_reason})...")
                    
                    # 尝试多个数据源获取历史价格（按优先级排序）
                    price = None
                    
                    # 第1层: 主流CEX交易所 (速度快，数据可靠)
                    price_sources_tier1 = [
                        ("Binance", get_historical_price_from_binance),
                        ("Bybit", get_historical_price_from_bybit),
                        ("OKX", get_historical_price_from_okx),
                    ]
                    
                    for source_name, source_func in price_sources_tier1:
                        if price is None:
                            if source_name != "Binance":  # Binance已在第一次尝试
                                print(f"   🔄 尝试 {source_name}...")
                            price = source_func(unit, date_str)
                        if price is not None:
                            break
                    
                    # 第2层: 其他CEX交易所
                    if price is None:
                        price_sources_tier2 = [
                            ("Bitget", get_historical_price_from_bitget),
                            ("Gate.io", get_historical_price_from_gateio),
                            ("KuCoin", get_historical_price_from_kucoin),
                        ]
                        
                        for source_name, source_func in price_sources_tier2:
                            if price is None:
                                print(f"   🔄 尝试 {source_name}...")
                                price = source_func(unit, date_str)
                            if price is not None:
                                break
                    
                    # 第3层: 数据聚合商 (覆盖面广，包含小币种)
                    if price is None:
                        price_sources_tier3 = [
                            ("CoinGecko", lambda u, d: get_historical_price_from_coingecko(u, d, coin_list)),
                            ("CryptoCompare", get_historical_price_from_cryptocompare),
                            ("CoinAPI", get_historical_price_from_coinapi),
                        ]
                        
                        for source_name, source_func in price_sources_tier3:
                            if price is None:
                                print(f"   🔄 尝试 {source_name}...")
                                price = source_func(unit, date_str)
                            if price is not None:
                                break

                if price is not None:
                    existing_prices[unit][date_str] = price
                    updated = True
                    if is_realtime:
                        print(f"✅ 实时价格已更新: {unit} = ${price}")
                    else:
                        print(f"✅ 历史价格已添加: {unit} 在 {date_str} = ${price}")
                else:
                    mode = "实时" if is_realtime else f"在 {date_str}"
                    print(f"❌ 警告: 未能从任何来源找到 {unit} 的{mode}价格。")
            else:
                if not is_realtime:
                    print(f"⏭️  跳过: {unit} 在 {date_str} ({update_reason})")
            
            # 实时模式下只更新一次
            if is_realtime:
                break

    return updated

def write_prices_to_frontmatter_md(prices, is_realtime=False):
    """将价格数据写入 token_prices.md 文件（frontmatter YAML格式，DataviewJS原生支持）。"""
    try:
        # 清理重复数据：确保每个代币-日期组合只有一个条目
        cleaned_prices = {}
        for token, date_prices in prices.items():
            if isinstance(date_prices, dict):
                cleaned_prices[token] = {}
                # 去重：如果有重复的日期，保留最新的价格
                for date, price in date_prices.items():
                    cleaned_prices[token][date] = price
        
        with open(PRICES_MD_PATH, 'w', encoding='utf-8') as f:
            # 写入 frontmatter，将价格数据直接放在YAML中
            f.write("---\n")
            if is_realtime:
                f.write("provider: Multi-Source CEX + Aggregators (Binance, Bybit, OKX, Bitget, Gate.io, KuCoin, CoinGecko, CryptoCompare, CoinAPI) Real-time\n")
            else:
                f.write("provider: Multi-Source CEX + Aggregators (Binance, Bybit, OKX, Bitget, Gate.io, KuCoin, CoinGecko, CryptoCompare, CoinAPI) Historical\n")
            f.write(f"last_updated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("format: frontmatter\n")
            f.write("prices:\n")
            
            # 将价格数据写入frontmatter YAML
            for token in sorted(cleaned_prices.keys()):
                f.write(f"  {token}:\n")
                date_prices = cleaned_prices[token]
                # 确保日期键和值的一致性，并按日期排序
                for date_key in sorted(date_prices.keys(), key=str):
                    price_value = date_prices[date_key]
                    f.write(f"    {date_key}: {price_value}\n")
            
            f.write("---\n\n")
            
            # 内容部分
            if is_realtime:
                f.write("# Token Prices (Historical + Real-time)\n\n")
            else:
                f.write("# Token Historical Prices\n\n")
            f.write("价格数据存储在frontmatter中，DataviewJS可直接访问。\n")
        
        mode = "包含实时" if is_realtime else "历史"
        total_tokens = len(cleaned_prices)
        total_combinations = sum(len(dates) for dates in cleaned_prices.values())
        print(f"✅ {mode}价格数据已成功写入: {PRICES_MD_PATH} (frontmatter格式)")
        print(f"📊 数据统计: {total_tokens} 个代币, {total_combinations} 个价格记录")
    except IOError as e:
        print(f"错误：无法写入价格文件: {e}")

def get_specific_token_data(tokens, dates=None):
    """为指定代币和日期创建数据结构。"""
    required = {}
    for token in tokens:
        token_upper = token.upper()
        if dates:
            required[token_upper] = set(dates)
        else:
            # 如果没有提供日期，使用空集合（实时模式下会被处理）
            required[token_upper] = set([""])
    return required

def parse_arguments():
    """解析命令行参数。"""
    parser = argparse.ArgumentParser(description='代币价格更新工具')
    parser.add_argument('--mode', choices=['historical', 'realtime'], default='historical',
                       help='选择获取模式: historical(历史价格) 或 realtime(实时价格)')
    parser.add_argument('--tokens', nargs='+', help='指定代币符号，例如: BTC ETH SOL')
    parser.add_argument('--dates', nargs='+', help='指定日期（仅历史模式），格式: YYYY-MM-DD')
    parser.add_argument('--parallel', action='store_true', default=True,
                       help='使用并行处理（默认开启，显著提升性能）')
    parser.add_argument('--max-concurrent-tokens', type=int, default=3,
                       help='最大并发代币数量（默认3，避免API限制）')
    parser.add_argument('--legacy', action='store_true', default=False,
                       help='使用传统同步处理（兼容模式，性能较慢）')
    return parser.parse_args()

async def main_async():
    """异步主函数"""
    args = parse_arguments()
    is_realtime = args.mode == 'realtime'
    use_parallel = args.parallel and not args.legacy
    max_concurrent = max(1, min(args.max_concurrent_tokens, 10))  # 限制在1-10之间
    
    mode_text = "实时" if is_realtime else "历史"
    perf_text = f"并行处理(并发数:{max_concurrent})" if use_parallel else "同步处理"
    print(f"🚀 开始更新{mode_text}代币价格 - {perf_text}")
    
    # 记录开始时间
    start_time = time.time()
    
    # 加载 CoinGecko 代币列表
    coingecko_list = get_coingecko_coin_list()
    if not coingecko_list:
        print("❌ 错误: 无法加载 CoinGecko 代币列表，程序终止。")
        return

    # 加载现有价格数据
    print("📊 加载现有价格数据...")
    existing_prices = load_existing_prices()
    print(f"✅ 已加载 {len(existing_prices)} 个代币的价格数据")

    # 根据参数获取需要的数据
    if args.tokens:
        # 指定代币模式
        if is_realtime:
            if args.dates:
                print("⚠️  警告: 实时模式下忽略日期参数")
            required = get_specific_token_data(args.tokens)
        else:
            # 历史模式下需要日期
            if not args.dates:
                print("❌ 错误: 历史模式下指定代币时必须提供日期参数")
                return
            required = get_specific_token_data(args.tokens, args.dates)
    else:
        # 默认模式：从 history 中获取所有数据
        if is_realtime:
            # 实时模式下获取所有代币的当前价格
            historical_data = find_required_data()
            required = {unit: set([""]) for unit in historical_data.keys()}
        else:
            # 历史模式下按原逻辑执行
            required = find_required_data()
    
    # 计算总任务数
    total_combinations = sum(len(dates) for dates in required.values())
    print(f"📈 需要获取{mode_text}价格的数据: {len(required)} 种代币, {total_combinations} 个代币-日期组合")
    
    # 更新价格数据
    if use_parallel:
        print(f"⚡ 使用并行处理，预计可提升性能 5-10 倍")
        updated = await update_price_data_async(required, existing_prices, coingecko_list, is_realtime, max_concurrent)
    else:
        print("🐌 使用传统同步处理")
        updated = update_price_data(required, existing_prices, coingecko_list, is_realtime)
    
    # 计算处理时间
    end_time = time.time()
    processing_time = end_time - start_time
    
    if updated:
        print(f"✅ {mode_text}价格数据已更新。")
    else:
        print(f"ℹ️  {mode_text}价格数据已是最新，无需更新。")
        
    # 写入frontmatter格式Markdown文件（唯一支持的格式）
    write_prices_to_frontmatter_md(existing_prices, is_realtime)
    
    # 性能报告
    if total_combinations > 0:
        avg_time_per_combination = processing_time / total_combinations
        print(f"⏱️  性能统计: 总耗时 {processing_time:.1f}秒, 平均每个组合 {avg_time_per_combination:.2f}秒")
        
        if use_parallel:
            estimated_sync_time = total_combinations * 2.8  # 估算同步处理时间
            speedup = estimated_sync_time / processing_time
            print(f"🚀 性能提升: 预计比同步处理快 {speedup:.1f}x")
    
    print("🎉 更新完成!")

if __name__ == "__main__":
    args = parse_arguments()
    
    # 如果使用并行处理，运行异步主函数
    if args.parallel and not args.legacy:
        try:
            asyncio.run(main_async())
        except KeyboardInterrupt:
            print("\n⚠️  用户中断操作")
        except Exception as e:
            print(f"❌ 程序执行出错: {e}")
            # 如果异步处理失败，回退到同步处理
            print("🔄 回退到同步处理...")
            args.legacy = True
    
    # 同步处理（兼容模式）
    if args.legacy or not args.parallel:
        is_realtime = args.mode == 'realtime'
        
        mode_text = "实时" if is_realtime else "历史"
        print(f"开始更新{mode_text}代币价格...")
        
        # 加载 CoinGecko 代币列表
        coingecko_list = get_coingecko_coin_list()
        if not coingecko_list:
            print("错误: 无法加载 CoinGecko 代币列表，程序终止。")
            exit()

        # 加载现有价格数据
        print("加载现有价格数据...")
        existing_prices = load_existing_prices()
        print(f"已加载 {len(existing_prices)} 个代币的价格数据")

        # 根据参数获取需要的数据
        if args.tokens:
            # 指定代币模式
            if is_realtime:
                if args.dates:
                    print("警告: 实时模式下忽略日期参数")
                required = get_specific_token_data(args.tokens)
            else:
                # 历史模式下需要日期
                if not args.dates:
                    print("错误: 历史模式下指定代币时必须提供日期参数")
                    exit()
                required = get_specific_token_data(args.tokens, args.dates)
        else:
            # 默认模式：从 history 中获取所有数据
            if is_realtime:
                # 实时模式下获取所有代币的当前价格
                historical_data = find_required_data()
                required = {unit: set([""]) for unit in historical_data.keys()}
            else:
                # 历史模式下按原逻辑执行
                required = find_required_data()
        
        print(f"需要获取{mode_text}价格的数据: {len(required)} 种代币")
        
        # 更新价格数据
        if update_price_data(required, existing_prices, coingecko_list, is_realtime):
            print(f"{mode_text}价格数据已更新。")
        else:
            print(f"{mode_text}价格数据已是最新，无需更新。")
            
        # 写入frontmatter格式Markdown文件（唯一支持的格式）
        write_prices_to_frontmatter_md(existing_prices, is_realtime)
        print("更新完成.")