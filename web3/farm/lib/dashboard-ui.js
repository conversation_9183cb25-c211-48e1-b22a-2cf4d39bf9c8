/**
 * Dashboard UI 模块
 * 
 * 负责用户界面组件和交互：
 * - 视图切换器
 * - 控制按钮
 * - 事件处理
 * 
 * @module DashboardUI
 */

// 确保核心对象存在
window.DashboardCore = window.DashboardCore || {};

/**
 * UI 组件
 */
window.DashboardCore.UI = {
    /**
     * 渲染视图切换器
     * @returns {string} HTML字符串
     */
    renderViewSwitcher() {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            const currentMainView = window.DashboardCore.ViewState?.mainView || 'risk';
            const currentDateGranularity = window.DashboardCore.ViewState?.dateGranularity || 'monthly';
            const currentRiskProjectStatus = window.DashboardCore.ViewState?.riskProjectStatus || 'Doing';

            return `
                <div class="dashboard-controls" style="
                    display: flex;
                    align-items: center;
                    gap: 15px;
                    margin-bottom: 15px;
                    padding: 12px;
                    background-color: var(--background-secondary);
                    border-radius: 6px;
                    border: 1px solid var(--background-modifier-border);
                    flex-wrap: wrap;
                    min-height: 50px;
                ">
                    <!-- 主视图切换 -->
                    <div class="view-switcher" style="display: flex; align-items: center; gap: 10px;">
                        <span style="font-weight: bold; color: var(--text-normal);">视图:</span>
                        <button 
                            onclick="window.DashboardCore.API.switchMainView('risk')" 
                            style="
                                padding: 6px 12px;
                                border: 1px solid var(--background-modifier-border);
                                border-radius: 4px;
                                background-color: ${currentMainView === 'risk' ? 'var(--interactive-accent)' : 'var(--background-primary)'};
                                color: ${currentMainView === 'risk' ? 'var(--text-on-accent)' : 'var(--text-normal)'};
                                cursor: pointer;
                                font-size: 12px;
                            "
                        >
                            风险维度
                        </button>
                        <button 
                            onclick="window.DashboardCore.API.switchMainView('date')" 
                            style="
                                padding: 6px 12px;
                                border: 1px solid var(--background-modifier-border);
                                border-radius: 4px;
                                background-color: ${currentMainView === 'date' ? 'var(--interactive-accent)' : 'var(--background-primary)'};
                                color: ${currentMainView === 'date' ? 'var(--text-on-accent)' : 'var(--text-normal)'};
                                cursor: pointer;
                                font-size: 12px;
                            "
                        >
                            日期维度
                        </button>
                    </div>

                    <!-- 日期粒度切换 (仅在日期维度时显示) -->
                    ${currentMainView === 'date' ? `
                        <div class="date-granularity-switcher" style="display: flex; align-items: center; gap: 10px;">
                            <span style="font-weight: bold; color: var(--text-normal);">粒度:</span>
                            <button 
                                onclick="window.DashboardCore.API.switchDateGranularity('daily')" 
                                style="
                                    padding: 4px 8px;
                                    border: 1px solid var(--background-modifier-border);
                                    border-radius: 3px;
                                    background-color: ${currentDateGranularity === 'daily' ? 'var(--interactive-accent)' : 'var(--background-primary)'};
                                    color: ${currentDateGranularity === 'daily' ? 'var(--text-on-accent)' : 'var(--text-normal)'};
                                    cursor: pointer;
                                    font-size: 11px;
                                "
                            >
                                日
                            </button>
                            <button 
                                onclick="window.DashboardCore.API.switchDateGranularity('weekly')" 
                                style="
                                    padding: 4px 8px;
                                    border: 1px solid var(--background-modifier-border);
                                    border-radius: 3px;
                                    background-color: ${currentDateGranularity === 'weekly' ? 'var(--interactive-accent)' : 'var(--background-primary)'};
                                    color: ${currentDateGranularity === 'weekly' ? 'var(--text-on-accent)' : 'var(--text-normal)'};
                                    cursor: pointer;
                                    font-size: 11px;
                                "
                            >
                                周
                            </button>
                            <button 
                                onclick="window.DashboardCore.API.switchDateGranularity('monthly')" 
                                style="
                                    padding: 4px 8px;
                                    border: 1px solid var(--background-modifier-border);
                                    border-radius: 3px;
                                    background-color: ${currentDateGranularity === 'monthly' ? 'var(--interactive-accent)' : 'var(--background-primary)'};
                                    color: ${currentDateGranularity === 'monthly' ? 'var(--text-on-accent)' : 'var(--text-normal)'};
                                    cursor: pointer;
                                    font-size: 11px;
                                "
                            >
                                月
                            </button>
                            <button 
                                onclick="window.DashboardCore.API.switchDateGranularity('yearly')" 
                                style="
                                    padding: 4px 8px;
                                    border: 1px solid var(--background-modifier-border);
                                    border-radius: 3px;
                                    background-color: ${currentDateGranularity === 'yearly' ? 'var(--interactive-accent)' : 'var(--background-primary)'};
                                    color: ${currentDateGranularity === 'yearly' ? 'var(--text-on-accent)' : 'var(--text-normal)'};
                                    cursor: pointer;
                                    font-size: 11px;
                                "
                            >
                                年
                            </button>
                        </div>
                    ` : ''}

                    <!-- 风险状态切换 (仅在风险维度时显示) -->
                    ${currentMainView === 'risk' ? `
                        <div class="risk-status-switcher" style="display: flex; align-items: center; gap: 10px;">
                            <span style="font-weight: bold; color: var(--text-normal);">状态:</span>
                            <button 
                                onclick="window.DashboardCore.API.switchRiskProjectStatus('Doing')" 
                                style="
                                    padding: 4px 8px;
                                    border: 1px solid var(--background-modifier-border);
                                    border-radius: 3px;
                                    background-color: ${currentRiskProjectStatus === 'Doing' ? 'var(--interactive-accent)' : 'var(--background-primary)'};
                                    color: ${currentRiskProjectStatus === 'Doing' ? 'var(--text-on-accent)' : 'var(--text-normal)'};
                                    cursor: pointer;
                                    font-size: 11px;
                                "
                            >
                                Doing
                            </button>
                            <button 
                                onclick="window.DashboardCore.API.switchRiskProjectStatus('Done')" 
                                style="
                                    padding: 4px 8px;
                                    border: 1px solid var(--background-modifier-border);
                                    border-radius: 3px;
                                    background-color: ${currentRiskProjectStatus === 'Done' ? 'var(--interactive-accent)' : 'var(--background-primary)'};
                                    color: ${currentRiskProjectStatus === 'Done' ? 'var(--text-on-accent)' : 'var(--text-normal)'};
                                    cursor: pointer;
                                    font-size: 11px;
                                "
                            >
                                Done
                            </button>
                        </div>
                    ` : ''}

                    <!-- 折叠展开控制按钮（仅在日期维度时显示） -->
                    ${currentMainView === 'date' ? `
                        <div class="collapse-controls" style="display: flex; align-items: center; gap: 5px; margin-left: auto;">
                            <button 
                                onclick="window.DashboardCore.UI.expandAllTimeGroups()" 
                                style="
                                    padding: 4px 8px;
                                    border: 1px solid var(--background-modifier-border);
                                    border-radius: 3px;
                                    background-color: var(--background-primary);
                                    color: var(--text-normal);
                                    cursor: pointer;
                                    font-size: 11px;
                                "
                                title="展开所有时间段的项目详情"
                            >
                                📂 全部展开
                            </button>
                            <button 
                                onclick="window.DashboardCore.UI.collapseAllTimeGroups()" 
                                style="
                                    padding: 4px 8px;
                                    border: 1px solid var(--background-modifier-border);
                                    border-radius: 3px;
                                    background-color: var(--background-primary);
                                    color: var(--text-normal);
                                    cursor: pointer;
                                    font-size: 11px;
                                "
                                title="收起所有时间段的项目详情"
                            >
                                📁 全部收起
                            </button>
                        </div>
                    ` : ''}

                    <!-- 刷新按钮 -->
                    <div class="refresh-controls" style="${currentMainView === 'date' ? '' : 'margin-left: auto;'}">
                        <button 
                            onclick="window.DashboardCore.API.refreshView()" 
                            style="
                                padding: 6px 12px;
                                border: 1px solid var(--background-modifier-border);
                                border-radius: 4px;
                                background-color: var(--background-primary);
                                color: var(--text-normal);
                                cursor: pointer;
                                font-size: 12px;
                            "
                            title="清除所有缓存并重新加载最新数据"
                        >
                            🔄 强制刷新
                        </button>
                    </div>
                </div>
            `;
        }, '<div>渲染控制器时出错</div>', '渲染视图切换器');
    },

    /**
     * 渲染统计摘要
     * @param {Object} stats - 统计数据
     * @returns {string} HTML字符串
     */
    renderStatsSummary(stats) {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            if (!stats) return '';

            const {
                totalProjects = 0,
                totalInvestment = 0,
                totalEarnings = 0,
                averageAPR = 0
            } = stats;

            return `
                <div class="dashboard-stats" style="
                    display: flex;
                    gap: 15px;
                    margin-bottom: 15px;
                    flex-wrap: wrap;
                ">
                    ${window.DashboardCore.Renderer.renderStatCard(
                        '项目总数',
                        totalProjects.toString(),
                        '个项目'
                    )}
                    ${window.DashboardCore.Renderer.renderStatCard(
                        '总投资',
                        window.DashboardCore.Utils.Number.formatNumber(totalInvestment),
                        'USDT'
                    )}
                    ${window.DashboardCore.Renderer.renderStatCard(
                        '总收益',
                        window.DashboardCore.Utils.Number.formatNumber(totalEarnings),
                        'USDT',
                        totalEarnings >= 0 ? '#28a745' : '#dc3545'
                    )}
                    ${window.DashboardCore.Renderer.renderStatCard(
                        '平均APR',
                        window.DashboardCore.Utils.Number.formatPercentage(averageAPR),
                        '',
                        averageAPR >= 0 ? '#28a745' : '#dc3545'
                    )}
                </div>
            `;
        }, '', '渲染统计摘要');
    },

    /**
     * 渲染分页控件
     * @param {number} currentPage - 当前页码
     * @param {number} totalPages - 总页数
     * @param {Function} onPageChange - 页码变化回调
     * @returns {string} HTML字符串
     */
    renderPagination(currentPage, totalPages, onPageChange) {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            if (totalPages <= 1) return '';

            let paginationHTML = `
                <div class="dashboard-pagination" style="
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 10px;
                    margin-top: 15px;
                    padding: 10px;
                ">
            `;

            // 上一页按钮
            if (currentPage > 1) {
                paginationHTML += `
                    <button onclick="${onPageChange}(${currentPage - 1})" style="
                        padding: 6px 12px;
                        border: 1px solid var(--background-modifier-border);
                        border-radius: 4px;
                        background-color: var(--background-primary);
                        color: var(--text-normal);
                        cursor: pointer;
                        font-size: 12px;
                    ">
                        ← 上一页
                    </button>
                `;
            }

            // 页码按钮
            const maxVisiblePages = 5;
            let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
            let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

            if (endPage - startPage + 1 < maxVisiblePages) {
                startPage = Math.max(1, endPage - maxVisiblePages + 1);
            }

            for (let i = startPage; i <= endPage; i++) {
                const isActive = i === currentPage;
                paginationHTML += `
                    <button onclick="${onPageChange}(${i})" style="
                        padding: 6px 10px;
                        border: 1px solid var(--background-modifier-border);
                        border-radius: 4px;
                        background-color: ${isActive ? 'var(--interactive-accent)' : 'var(--background-primary)'};
                        color: ${isActive ? 'var(--text-on-accent)' : 'var(--text-normal)'};
                        cursor: pointer;
                        font-size: 12px;
                        min-width: 32px;
                    ">
                        ${i}
                    </button>
                `;
            }

            // 下一页按钮
            if (currentPage < totalPages) {
                paginationHTML += `
                    <button onclick="${onPageChange}(${currentPage + 1})" style="
                        padding: 6px 12px;
                        border: 1px solid var(--background-modifier-border);
                        border-radius: 4px;
                        background-color: var(--background-primary);
                        color: var(--text-normal);
                        cursor: pointer;
                        font-size: 12px;
                    ">
                        下一页 →
                    </button>
                `;
            }

            paginationHTML += `
                    <span style="margin-left: 15px; color: var(--text-muted); font-size: 12px;">
                        第 ${currentPage} 页，共 ${totalPages} 页
                    </span>
                </div>
            `;

            return paginationHTML;
        }, '', '渲染分页控件');
    },

    /**
     * 绑定事件处理器
     */
    bindEvents() {
        // 初始化状态管理器
        window.DashboardCore.CollapseState.init();
        
        // 绑定展开/收起按钮的点击事件
        this.bindCollapseExpandEvents();
        
        // 这里可以添加额外的事件绑定逻辑
        // 大部分事件已经通过 onclick 属性直接绑定
        
        if (window.DashboardCore.getConfig('DEBUG.ENABLE_CONSOLE_LOG', false)) {
            console.log('Dashboard UI events bound');
        }
    },

    /**
     * 绑定折叠/展开事件处理器
     */
    bindCollapseExpandEvents() {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            // 等待DOM更新后再绑定事件
            setTimeout(() => {
                // 绑定展开/收起按钮的点击事件
                const toggleButtons = document.querySelectorAll('.expand-toggle');
                toggleButtons.forEach(button => {
                    button.addEventListener('click', (e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        
                        const targetGroupId = button.getAttribute('data-target');
                        if (targetGroupId) {
                            this.toggleTimeGroup(targetGroupId, button);
                        }
                    });
                });
                
                // 恢复之前保存的展开/收起状态
                this.restoreCollapseState();
            }, 100);
        }, null, '绑定折叠/展开事件');
    },

    /**
     * 切换时间组的展开/收起状态
     * @param {string} timeGroupId - 时间组ID
     * @param {HTMLElement} toggleButton - 切换按钮
     */
    toggleTimeGroup(timeGroupId, toggleButton) {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            // 查找该时间组的所有详情行
            const detailRows = document.querySelectorAll(`[data-time-group="${timeGroupId}"]`);
            
            if (detailRows.length === 0) {
                console.warn(`未找到时间组 ${timeGroupId} 的详情行`);
                return;
            }

            // 判断当前状态
            const isExpanded = detailRows[0].classList.contains('expanded');
            
            // 切换状态
            if (isExpanded) {
                // 收起
                detailRows.forEach(row => {
                    row.classList.remove('expanded');
                    row.style.display = 'none';
                });
                toggleButton.textContent = '▶️';
                toggleButton.setAttribute('title', '展开项目详情');
            } else {
                // 展开
                detailRows.forEach(row => {
                    row.classList.add('expanded');
                    row.style.display = 'table-row';
                });
                toggleButton.textContent = '▼️';
                toggleButton.setAttribute('title', '收起项目详情');
            }

            // 更新状态管理
            if (window.DashboardCore.CollapseState) {
                window.DashboardCore.CollapseState.setGroupState(timeGroupId, !isExpanded);
            }
        }, null, '切换时间组状态');
    },

    /**
     * 添加全局展开/收起控制按钮
     */
    addGlobalCollapseControls() {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            // 查找控制区域
            const controlsContainer = document.querySelector('.dashboard-controls .refresh-controls');
            if (!controlsContainer) {
                console.warn('未找到控制区域，无法添加全局展开/收起按钮');
                return;
            }

            // 创建全局控制按钮
            const globalControls = document.createElement('div');
            globalControls.className = 'global-collapse-controls';
            globalControls.style.cssText = `
                display: flex;
                gap: 5px;
                margin-right: 10px;
            `;

            // 全部展开按钮
            const expandAllButton = document.createElement('button');
            expandAllButton.textContent = '📂 全部展开';
            expandAllButton.style.cssText = `
                padding: 6px 12px;
                border: 1px solid var(--background-modifier-border);
                border-radius: 4px;
                background-color: var(--background-primary);
                color: var(--text-normal);
                cursor: pointer;
                font-size: 12px;
            `;
            expandAllButton.title = '展开所有时间段的项目详情';
            expandAllButton.addEventListener('click', () => this.expandAllTimeGroups());

            // 全部收起按钮
            const collapseAllButton = document.createElement('button');
            collapseAllButton.textContent = '📁 全部收起';
            collapseAllButton.style.cssText = `
                padding: 6px 12px;
                border: 1px solid var(--background-modifier-border);
                border-radius: 4px;
                background-color: var(--background-primary);
                color: var(--text-normal);
                cursor: pointer;
                font-size: 12px;
            `;
            collapseAllButton.title = '收起所有时间段的项目详情';
            collapseAllButton.addEventListener('click', () => this.collapseAllTimeGroups());

            globalControls.appendChild(expandAllButton);
            globalControls.appendChild(collapseAllButton);
            
            // 插入到刷新按钮之前
            controlsContainer.parentNode.insertBefore(globalControls, controlsContainer);
        }, null, '添加全局展开/收起控制');
    },

    /**
     * 展开所有时间组
     */
    expandAllTimeGroups() {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            const toggleButtons = document.querySelectorAll('.expand-toggle');
            toggleButtons.forEach(button => {
                const targetGroupId = button.getAttribute('data-target');
                if (targetGroupId) {
                    const detailRows = document.querySelectorAll(`[data-time-group="${targetGroupId}"]`);
                    const isExpanded = detailRows.length > 0 && detailRows[0].classList.contains('expanded');
                    
                    if (!isExpanded) {
                        this.toggleTimeGroup(targetGroupId, button);
                    }
                }
            });
        }, null, '展开所有时间组');
    },

    /**
     * 收起所有时间组
     */
    collapseAllTimeGroups() {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            const toggleButtons = document.querySelectorAll('.expand-toggle');
            toggleButtons.forEach(button => {
                const targetGroupId = button.getAttribute('data-target');
                if (targetGroupId) {
                    const detailRows = document.querySelectorAll(`[data-time-group="${targetGroupId}"]`);
                    const isExpanded = detailRows.length > 0 && detailRows[0].classList.contains('expanded');
                    
                    if (isExpanded) {
                        this.toggleTimeGroup(targetGroupId, button);
                    }
                }
            });
        }, null, '收起所有时间组');
    },

    /**
     * 根据状态管理恢复展开/收起状态
     */
    restoreCollapseState() {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            if (!window.DashboardCore.CollapseState) {
                return;
            }

            const toggleButtons = document.querySelectorAll('.expand-toggle');
            toggleButtons.forEach(button => {
                const targetGroupId = button.getAttribute('data-target');
                if (targetGroupId) {
                    const savedState = window.DashboardCore.CollapseState.getGroupState(targetGroupId);
                    if (savedState) {
                        // 如果保存的状态是展开的，则展开
                        const detailRows = document.querySelectorAll(`[data-time-group="${targetGroupId}"]`);
                        const isCurrentlyExpanded = detailRows.length > 0 && detailRows[0].classList.contains('expanded');
                        
                        if (savedState && !isCurrentlyExpanded) {
                            this.toggleTimeGroup(targetGroupId, button);
                        }
                    }
                }
            });
        }, null, '恢复展开/收起状态');
    }
};

/**
 * 折叠/展开状态管理器
 */
window.DashboardCore.CollapseState = {
    /**
     * 存储展开/收起状态的Map
     */
    stateMap: new Map(),
    
    /**
     * 本地存储的key
     */
    STORAGE_KEY: 'dashboard-collapse-state',
    
    /**
     * 获取时间组的展开状态
     * @param {string} timeGroupId - 时间组ID
     * @returns {boolean} 是否展开
     */
    getGroupState(timeGroupId) {
        return this.stateMap.get(timeGroupId) || false;
    },
    
    /**
     * 设置时间组的展开状态
     * @param {string} timeGroupId - 时间组ID
     * @param {boolean} isExpanded - 是否展开
     */
    setGroupState(timeGroupId, isExpanded) {
        this.stateMap.set(timeGroupId, isExpanded);
        this.saveToStorage();
    },
    
    /**
     * 获取所有时间组的状态
     * @returns {Object} 状态对象
     */
    getAllStates() {
        const states = {};
        this.stateMap.forEach((value, key) => {
            states[key] = value;
        });
        return states;
    },
    
    /**
     * 设置所有时间组的状态
     * @param {Object} states - 状态对象
     */
    setAllStates(states) {
        this.stateMap.clear();
        Object.entries(states).forEach(([key, value]) => {
            this.stateMap.set(key, value);
        });
        this.saveToStorage();
    },
    
    /**
     * 清除所有状态
     */
    clearAllStates() {
        this.stateMap.clear();
        this.saveToStorage();
    },
    
    /**
     * 保存状态到本地存储
     */
    saveToStorage() {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            const states = this.getAllStates();
            localStorage.setItem(this.STORAGE_KEY, JSON.stringify(states));
        }, null, '保存折叠状态到本地存储');
    },
    
    /**
     * 从本地存储加载状态
     */
    loadFromStorage() {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            const savedStates = localStorage.getItem(this.STORAGE_KEY);
            if (savedStates) {
                try {
                    const states = JSON.parse(savedStates);
                    this.setAllStates(states);
                    return true;
                } catch (error) {
                    console.warn('加载折叠状态失败:', error);
                    return false;
                }
            }
            return false;
        }, false, '从本地存储加载折叠状态');
    },
    
    /**
     * 初始化状态管理器
     */
    init() {
        this.loadFromStorage();
        
        // 监听页面卸载，保存状态
        window.addEventListener('beforeunload', () => {
            this.saveToStorage();
        });
    }
};

// 向后兼容的全局函数
window.DashboardUI = window.DashboardCore.UI;

// 确保全局控制函数可以被onclick调用
window.DashboardCore.UI.expandAllTimeGroups = window.DashboardCore.UI.expandAllTimeGroups.bind(window.DashboardCore.UI);
window.DashboardCore.UI.collapseAllTimeGroups = window.DashboardCore.UI.collapseAllTimeGroups.bind(window.DashboardCore.UI);

console.log('Dashboard UI module loaded');
