/**
 * Web3 Farm Dashboard 核心库
 * 
 * 这是一个模块化的 JavaScript 库，用于支持 Obsidian DataviewJS 的 Web3 农场投资仪表板
 * 
 * 主要功能：
 * - 数据加载和解析
 * - 时间数据聚合
 * - 基础和衍生指标计算
 * - 表格构建和渲染
 * - UI 组件和交互
 * 
 * 使用方法：
 * ```javascript
 * // 在 DataviewJS 代码块中导入
 * await import('web3/farm/lib/dashboard-core.js');
 * 
 * // 使用全局对象
 * const data = await window.DashboardCore.loadAllData(dv);
 * const table = window.DashboardCore.buildRiskTable(data);
 * ```
 * 
 * @version 3.0.0
 * <AUTHOR> Agent
 * @created 2025-07-10
 */

// ===== 核心配置 =====
window.DashboardCore = window.DashboardCore || {};

// 注意：子模块已在 dashboard.md 中按依赖顺序加载
// 这里不需要再次导入，因为 Obsidian DataviewJS 不支持 ES6 import

/**
 * 验证依赖模块是否已加载
 * @returns {boolean} 是否所有依赖都已加载
 */
window.DashboardCore.validateDependencies = function() {
    const requiredModules = [
        'DefaultConfig',
        'Utils',
        'DataLoader',
        'BaseCalculator',
        'USDTCalculator',
        'DerivedCalculator',
        'TableBuilder',
        'Renderer',
        'UI'
    ];

    const missing = [];
    for (const module of requiredModules) {
        if (!window.DashboardCore[module]) {
            missing.push(module);
        }
    }

    if (missing.length > 0) {
        console.error('缺少依赖模块:', missing);
        return false;
    }

    return true;
};

/**
 * 核心 API 接口
 * 提供统一的入口点供外部调用
 */
window.DashboardCore.API = {
    /**
     * 初始化仪表板
     * @param {Object} dv - Dataview 对象
     * @param {Object} config - 配置对象
     * @returns {Promise<void>}
     */
    async initialize(dv, config = {}) {
        // 验证依赖模块
        if (!window.DashboardCore.validateDependencies()) {
            throw new Error('Dashboard Core 初始化失败：缺少必要的依赖模块');
        }

        // 设置全局 dataview 引用
        window.DashboardCore.dv = dv;

        // 合并配置
        window.DashboardCore.Config = {
            ...window.DashboardCore.DefaultConfig,
            ...config
        };

        // 初始化视图状态
        window.DashboardCore.ViewState = {
            mainView: 'risk', // 'risk' | 'date'
            dateGranularity: 'monthly', // 'daily' | 'weekly' | 'monthly' | 'yearly'
            riskProjectStatus: 'Doing', // 'Doing' | 'Done' - 风险视图的项目状态过滤
            currentPage: 1
        };

        console.log('Dashboard Core initialized successfully');
    },

    /**
     * 加载所有基础数据
     * @returns {Promise<Object>} 包含所有基础数据的对象
     */
    async loadAllData() {
        return await window.DashboardCore.DataLoader.loadAllData();
    },

    /**
     * 构建风险维度表格
     * @param {Object} data - 基础数据
     * @param {string} projectStatus - 项目状态过滤 ('Doing', 'Done', 'all')
     * @returns {Object} 表格数据和表头
     */
    buildRiskTable(data, projectStatus = 'Doing') {
        return window.DashboardCore.TableBuilder.buildRiskTable(data, projectStatus);
    },

    /**
     * 构建日期维度表格
     * @param {Object} data - 基础数据
     * @param {string} granularity - 时间粒度
     * @returns {Object} 表格数据和表头
     */
    buildDateTable(data, granularity = 'monthly') {
        // 根据粒度调整第一列标题
        const firstColumnTitle = granularity === 'daily' ? '日期' :
                               granularity === 'weekly' ? '周' :
                               granularity === 'monthly' ? '月份' : '年份';

        // 检查缓存
        if (window.DashboardDateTableData && window.DashboardDateTableData[granularity]) {
            console.log(`使用缓存的${granularity}粒度日期表格数据`);
            return {
                headers: [
                    firstColumnTitle, '投入金额', '当前占比', '健康程度', '项目', '状态',
                    '总收益', '投资天数', '日收益', '当前APR', '期望APR', 'APR Gap',
                    '当前APR总比', '期望APR总比', 'APR Gap总比'
                ],
                data: window.DashboardDateTableData[granularity]
            };
        }

        // 构建新的日期表格数据
        const result = window.DashboardCore.TableBuilder.buildDateTable(data, granularity);

        // 缓存结果
        if (!window.DashboardDateTableData) {
            window.DashboardDateTableData = {};
        }
        window.DashboardDateTableData[granularity] = result.data;

        console.log(`构建并缓存${granularity}粒度日期表格数据，行数:`, result.data.length);

        return result;
    },

    /**
     * 渲染表格
     * @param {Array} headers - 表头数组
     * @param {Array} data - 表格数据
     * @param {string} containerId - 容器ID
     * @returns {string} HTML 字符串
     */
    renderTable(headers, data, containerId = 'dashboard-table') {
        return window.DashboardCore.Renderer.renderFixedTable(headers, data, containerId);
    },

    /**
     * 渲染完整的仪表板
     * @param {string} containerId - 容器ID
     * @returns {Promise<void>}
     */
    async renderDashboard(containerId = 'dashboard-container') {
        try {
            // 加载数据并设置全局缓存
            const data = await this.loadAllData();

            // 设置全局缓存变量（兼容原版）
            this.setupGlobalCache(data);

            // 构建当前视图的表格
            let tableResult;
            if (window.DashboardCore.ViewState.mainView === 'risk') {
                tableResult = this.buildRiskTable(data, window.DashboardCore.ViewState.riskProjectStatus);
            } else {
                tableResult = this.buildDateTable(data, window.DashboardCore.ViewState.dateGranularity);
            }

            // 渲染 UI
            const uiHTML = window.DashboardCore.UI.renderViewSwitcher();
            const tableHTML = this.renderTable(tableResult.headers, tableResult.data, 'dashboard-table');

            // 组合完整的 HTML
            const fullHTML = `
                <div id="${containerId}">
                    ${uiHTML}
                    ${tableHTML}
                </div>
            `;

            return fullHTML;
        } catch (error) {
            console.error('渲染仪表板时出错:', error);
            return `<div style="color: red;">渲染仪表板时出错: ${error.message}</div>`;
        }
    },

    /**
     * 设置全局缓存变量（兼容原版）
     * @param {Object} data - 基础数据
     */
    setupGlobalCache(data) {
        try {
            // 构建风险维度表格数据并缓存（使用当前状态过滤）
            const riskTableResult = this.buildRiskTable(data, window.DashboardCore.ViewState.riskProjectStatus);
            window.DashboardTableData = riskTableResult.data;

            // 设置日期维度基础数据缓存
            window.DashboardDateBaseData = {
                projects: data.allProjects,
                riskInfo: data.riskInfo,
                levelInfo: data.levelInfo,
                priceMap: data.priceMap
            };

            // 初始化日期维度表格数据缓存
            if (!window.DashboardDateTableData) {
                window.DashboardDateTableData = {};
            }

            console.log('全局缓存设置完成:', {
                riskTableRows: window.DashboardTableData.length,
                allProjects: data.allProjects.length,
                doingProjects: data.doingProjects.length,
                currentRiskStatus: window.DashboardCore.ViewState.riskProjectStatus
            });
        } catch (error) {
            console.error('设置全局缓存失败:', error);
        }
    },

    /**
     * 切换主视图
     * @param {string} viewType - 视图类型 ('risk' | 'date')
     * @returns {Promise<void>}
     */
    async switchMainView(viewType) {
        window.DashboardCore.ViewState.mainView = viewType;
        await this.refreshView();
    },

    /**
     * 切换日期粒度
     * @param {string} granularity - 时间粒度
     * @returns {Promise<void>}
     */
    async switchDateGranularity(granularity) {
        window.DashboardCore.ViewState.dateGranularity = granularity;
        // 清除缓存以确保重新计算
        if (window.DashboardDateTableData) {
            delete window.DashboardDateTableData[granularity];
        }
        if (window.DashboardCore.ViewState.mainView === 'date') {
            await this.refreshView();
        }
    },

    /**
     * 切换风险视图项目状态
     * @param {string} status - 项目状态 ('Doing' | 'Done')
     * @returns {Promise<void>}
     */
    async switchRiskProjectStatus(status) {
        window.DashboardCore.ViewState.riskProjectStatus = status;
        if (window.DashboardCore.ViewState.mainView === 'risk') {
            await this.refreshView();
        }
    },

    /**
     * 刷新当前视图（清除所有缓存并重新加载数据）
     * @returns {Promise<void>}
     */
    async refreshView() {
        try {
            console.log('🔄 开始刷新视图，清除所有缓存...');
            
            // 1. 清除数据层缓存
            if (window.DashboardCore.DataLoader && window.DashboardCore.DataLoader.Cache) {
                window.DashboardCore.DataLoader.Cache.clear();
                console.log('✅ 数据层缓存已清除');
            }
            
            // 2. 清除表格层缓存（全局变量）
            if (typeof window.DashboardTableData !== 'undefined') {
                delete window.DashboardTableData;
                console.log('✅ 风险维度表格缓存已清除');
            }
            
            if (typeof window.DashboardDateBaseData !== 'undefined') {
                delete window.DashboardDateBaseData;
                console.log('✅ 日期维度基础数据缓存已清除');
            }
            
            if (typeof window.DashboardDateTableData !== 'undefined') {
                delete window.DashboardDateTableData;
                console.log('✅ 日期维度表格缓存已清除');
            }
            
            // 3. 重新渲染界面
            const container = document.getElementById('dashboard-container');
            if (container) {
                const newHTML = await this.renderDashboard('dashboard-container');
                container.outerHTML = newHTML;
                
                // 重新绑定事件
                window.DashboardCore.UI.bindEvents();
                console.log('✅ 视图刷新完成');
            } else {
                console.warn('⚠️ 未找到dashboard-container容器');
            }
        } catch (error) {
            console.error('❌ 刷新视图时出错:', error);
            // 尝试简单的界面重新渲染
            const container = document.getElementById('dashboard-container');
            if (container) {
                const newHTML = await this.renderDashboard('dashboard-container');
                container.outerHTML = newHTML;
                window.DashboardCore.UI.bindEvents();
            }
        }
    }
};

/**
 * 向后兼容的全局函数
 * 保持与原有代码的兼容性
 */
window.switchMainView = (viewType) => {
    window.DashboardCore.API.switchMainView(viewType);
};

window.switchDateView = (granularity) => {
    window.DashboardCore.API.switchDateGranularity(granularity);
};

window.renderCurrentView = () => {
    window.DashboardCore.API.refreshView();
};

console.log('Dashboard Core library loaded successfully');
