/**
 * Dashboard 计算模块
 * 
 * 包含所有核心计算逻辑：
 * - 基础指标计算（APR、投资天数、USDT转换等）
 * - 衍生指标计算（仓位状态、APR Gap、组合APR等）
 * - 时间范围处理
 * - 项目数据处理
 * 
 * @module DashboardCalculator
 */

// 确保核心对象存在
window.DashboardCore = window.DashboardCore || {};

/**
 * 基础计算器
 */
window.DashboardCore.BaseCalculator = {
    /**
     * 计算项目的历史记录数据
     * @param {Object} project - 项目对象
     * @param {Map} priceMap - 价格映射
     * @returns {Object|null} 历史数据对象
     */
    calculateProjectHistory(project, priceMap) {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            if (!window.DashboardCore.Utils.Validation.isValidProject(project)) {
                return null;
            }

            const balanceEntries = project.file.lists.filter(item =>
                window.DashboardCore.Utils.Validation.isValidBalanceEntry(item)
            );

            if (balanceEntries.length === 0) return null;

            // 按日期排序
            balanceEntries.sort((a, b) => {
                const dateA = window.DashboardCore.Utils.Date.parseDate(a.date);
                const dateB = window.DashboardCore.Utils.Date.parseDate(b.date);
                if (!dateA || !dateB) return 0;
                return dateA.toMillis() - dateB.toMillis();
            });

            const firstEntry = balanceEntries[0];
            const lastEntry = balanceEntries.length === 1 ? balanceEntries[0] : balanceEntries[balanceEntries.length - 1];

            // 计算历史上所有add属性的总和
            let totalAddAmount = 0;
            for (const entry of balanceEntries) {
                if (entry && entry.add !== undefined && entry.add !== null) {
                    const addValue = window.DashboardCore.Utils.Number.parseNumber(entry.add);
                    totalAddAmount += addValue;
                }
            }

            // 计算历史上所有debt属性的总和（新增：债务计算）
            let totalDebtAmount = 0;
            for (const entry of balanceEntries) {
                if (entry && entry.debt !== undefined && entry.debt !== null) {
                    // 解析debt字段，支持 "16690 USDT" 格式
                    const debtStr = entry.debt.toString().trim();
                    const debtMatch = debtStr.match(/^([\d.]+)/);
                    if (debtMatch) {
                        const debtValue = window.DashboardCore.Utils.Number.parseNumber(debtMatch[1]);
                        totalDebtAmount += debtValue;
                    }
                }
            }

            const firstBalance = window.DashboardCore.Utils.Number.parseNumber(firstEntry.balance);
            const lastBalance = window.DashboardCore.Utils.Number.parseNumber(lastEntry.balance);
            const adjustedFirstBalance = firstBalance + totalAddAmount;

            return {
                firstEntry,
                lastEntry,
                firstBalance: adjustedFirstBalance,
                lastBalance,
                totalAddAmount,
                totalDebtAmount,  // 新增：总债务金额
                balanceEntries
            };
        }, null, '计算项目历史数据');
    },

    /**
     * 计算项目在指定时间范围内的历史记录数据
     * @param {Object} project - 项目对象
     * @param {Map} priceMap - 价格映射
     * @param {string} startDate - 开始日期
     * @param {string} endDate - 结束日期
     * @returns {Object|null} 时间范围内的历史数据对象
     */
    calculateProjectHistoryInTimeRange(project, priceMap, startDate, endDate) {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            if (!window.DashboardCore.Utils.Validation.isValidProject(project)) {
                return null;
            }

            const balanceEntries = project.file.lists.filter(item =>
                window.DashboardCore.Utils.Validation.isValidBalanceEntry(item)
            );

            if (balanceEntries.length === 0) return null;

            // 按日期排序
            balanceEntries.sort((a, b) => {
                const dateA = window.DashboardCore.Utils.Date.parseDate(a.date);
                const dateB = window.DashboardCore.Utils.Date.parseDate(b.date);
                if (!dateA || !dateB) return 0;
                return dateA.toMillis() - dateB.toMillis();
            });

            // 只处理时间范围内有数据的情况
            const entriesInRange = balanceEntries.filter(entry => {
                const entryDate = window.DashboardCore.Utils.Date.parseDate(entry.date);
                if (!entryDate) return false;

                const entryDateStr = window.DashboardCore.Utils.Date.formatDate(entryDate, 'yyyy-MM-dd');
                return entryDateStr >= startDate && entryDateStr <= endDate;
            });

            // 如果时间范围内没有记录，直接返回null
            if (entriesInRange.length === 0) return null;

            const firstEntry = entriesInRange[0];
            const lastEntry = entriesInRange.length === 1 ? entriesInRange[0] : entriesInRange[entriesInRange.length - 1];

            // 计算时间范围内所有add属性的总和
            let totalAddAmount = 0;
            for (const entry of entriesInRange) {
                if (entry && entry.add !== undefined && entry.add !== null) {
                    const addValue = window.DashboardCore.Utils.Number.parseNumber(entry.add);
                    totalAddAmount += addValue;
                }
            }

            const firstBalance = window.DashboardCore.Utils.Number.parseNumber(firstEntry.balance);
            const lastBalance = window.DashboardCore.Utils.Number.parseNumber(lastEntry.balance);
            const adjustedFirstBalance = firstBalance + totalAddAmount;

            return {
                firstEntry,
                lastEntry,
                firstBalance: adjustedFirstBalance,
                lastBalance,
                totalAddAmount,
                balanceEntries: entriesInRange
            };
        }, null, '计算时间范围内项目历史数据');
    },

    /**
     * 计算APR
     * @param {Object} historyData - 历史数据对象
     * @returns {number} APR值
     */
    calculateAPR(historyData) {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            if (!historyData) return 0;

            const firstDate = window.DashboardCore.Utils.Date.parseDate(historyData.firstEntry.date);
            const lastDate = window.DashboardCore.Utils.Date.parseDate(historyData.lastEntry.date);

            if (!firstDate || !lastDate) return 0;

            const daysDiff = window.DashboardCore.Utils.Date.daysBetween(firstDate, lastDate);
            const totalEarned = historyData.lastBalance - historyData.firstBalance;
            const baseAmount = historyData.firstBalance;

            if (baseAmount > 0) {
                const returnRate = totalEarned / baseAmount;
                return Math.round(((returnRate / daysDiff) * 365) * 100 * 100) / 100;
            }

            return 0;
        }, 0, '计算APR');
    },

    /**
     * 计算投资天数
     * @param {Object} historyData - 历史数据对象
     * @returns {number} 投资天数
     */
    calculateInvestDays(historyData) {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            if (!historyData) return 0;

            const firstDate = window.DashboardCore.Utils.Date.parseDate(historyData.firstEntry.date);
            const lastDate = window.DashboardCore.Utils.Date.parseDate(historyData.lastEntry.date);

            if (!firstDate || !lastDate) return 0;

            const daysDiff = window.DashboardCore.Utils.Date.daysBetween(firstDate, lastDate);
            return Math.round(daysDiff * 100) / 100;
        }, 0, '计算投资天数');
    },

    /**
     * 计算日期维度的投资天数（使用项目完整历史的第一个日期）
     * @param {Object} project - 项目对象
     * @param {string} currentDate - 当前日期
     * @returns {number} 投资天数
     */
    calculateInvestDaysForDateView(project, currentDate) {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            if (!window.DashboardCore.Utils.Validation.isValidProject(project)) {
                return 0;
            }

            // 获取项目的所有balance记录
            const balanceEntries = project.file.lists.filter(item =>
                window.DashboardCore.Utils.Validation.isValidBalanceEntry(item)
            );

            if (balanceEntries.length === 0) return 0;

            // 按日期排序，获取最早的记录
            balanceEntries.sort((a, b) => {
                const dateA = window.DashboardCore.Utils.Date.parseDate(a.date);
                const dateB = window.DashboardCore.Utils.Date.parseDate(b.date);
                if (!dateA || !dateB) return 0;
                return dateA.toMillis() - dateB.toMillis();
            });

            const firstDate = window.DashboardCore.Utils.Date.parseDate(balanceEntries[0].date);
            const endDate = window.DashboardCore.Utils.Date.parseDate(currentDate);

            if (!firstDate || !endDate) return 0;

            const daysDiff = window.DashboardCore.Utils.Date.daysBetween(firstDate, endDate);
            return Math.round(daysDiff * 100) / 100;
        }, 0, '计算日期维度投资天数');
    }
};

/**
 * USDT 转换和价格计算器
 */
window.DashboardCore.USDTCalculator = {
    /**
     * 计算USDT投入金额（统一价值公式：支持所有项目类型）
     * 
     * 统一公式：收益 = (最终价格 × 最终数量) - (初始价格 × 初始数量) - 追加投资价值
     * 
     * 适用于：
     * - Trade项目：纯价格收益（数量不变）
     * - DeFi项目：数量收益 + 价格收益
     * - 合约项目：头寸价值变化
     * 
     * @param {Object} historyData - 历史数据对象
     * @param {string} unit - 代币单位
     * @param {Map} priceMap - 价格映射
     * @returns {Object} USDT金额对象
     */
    calculateUSDTAmount(historyData, unit, priceMap) {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            if (!historyData) {
                return { investAmount: 0, withdrawAmount: 0, priceFound: false };
            }
            
            // 对于稳定币，价格恒为1，应用统一公式
            // 收益 = (1 × lastBalance) - (1 × firstBalance) - (1 × totalAddAmount)
            if (window.DashboardCore.isStableCoin(unit)) {
                const firstBalance = window.DashboardCore.Utils.Number.parseNumber(historyData.firstEntry.balance);
                const lastBalance = historyData.lastBalance;
                const totalAddAmount = historyData.totalAddAmount;

                return {
                    investAmount: firstBalance + totalAddAmount,  // 总投资 = 初始 + 追加
                    withdrawAmount: lastBalance,                  // 当前价值 = 最终余额
                    priceFound: true,
                    investDate: window.DashboardCore.Utils.Date.formatDate(
                        window.DashboardCore.Utils.Date.parseDate(historyData.firstEntry.date), 'yyyy-MM-dd'
                    ),
                    withdrawDate: window.DashboardCore.Utils.Date.formatDate(
                        window.DashboardCore.Utils.Date.parseDate(historyData.lastEntry.date), 'yyyy-MM-dd'
                    )
                };
            }

            // 获取价格数据
            const prices = priceMap.get(unit);
            
            if (!prices) {
                return { 
                    investAmount: -1, 
                    withdrawAmount: -1, 
                    priceFound: false,
                    missingPriceToken: unit,
                    missingPriceDate: 'unknown'
                };
            }

            // 应用统一公式计算非稳定币的USDT价值
            // 总投资 = (初始价格 × 初始数量) + Σ(追加价格 × 追加数量)
            // 当前价值 = 最终价格 × 最终数量
            // 收益 = 当前价值 - 总投资
            
            let totalInvestAmount = 0;
            let allPricesFound = true;
            let missingPriceDate = null;
            let missingPriceToken = null;

            // 按日期排序的历史记录
            const sortedEntries = [...historyData.balanceEntries].sort((a, b) => {
                const dateA = window.DashboardCore.Utils.Date.parseDate(a.date);
                const dateB = window.DashboardCore.Utils.Date.parseDate(b.date);
                return dateA.toMillis() - dateB.toMillis();
            });

            // 处理初始投资
            const firstEntry = sortedEntries[0];
            const firstDate = window.DashboardCore.Utils.Date.formatDate(
                window.DashboardCore.Utils.Date.parseDate(firstEntry.date), 'yyyy-MM-dd'
            );
            const firstBalance = window.DashboardCore.Utils.Number.parseNumber(firstEntry.balance);
            
            if (prices.has(firstDate)) {
                const firstPrice = prices.get(firstDate);
                totalInvestAmount += firstBalance * firstPrice;
            } else {
                allPricesFound = false;
                if (!missingPriceDate) {
                    missingPriceDate = firstDate;
                    missingPriceToken = unit;
                }
            }

            // 处理追加投资
            for (const entry of sortedEntries) {
                if (entry.add !== undefined && entry.add !== null) {
                    const addAmount = window.DashboardCore.Utils.Number.parseNumber(entry.add);
                    if (addAmount > 0) {
                        const addDate = window.DashboardCore.Utils.Date.formatDate(
                            window.DashboardCore.Utils.Date.parseDate(entry.date), 'yyyy-MM-dd'
                        );
                        if (prices.has(addDate)) {
                            const addPrice = prices.get(addDate);
                            totalInvestAmount += addAmount * addPrice;
                        } else {
                            allPricesFound = false;
                            if (!missingPriceDate) {
                                missingPriceDate = addDate;
                                missingPriceToken = unit;
                            }
                        }
                    }
                }
            }

            // 计算当前价值（使用最新日期的价格）
            const lastDate = window.DashboardCore.Utils.Date.formatDate(
                window.DashboardCore.Utils.Date.parseDate(historyData.lastEntry.date), 'yyyy-MM-dd'
            );
            let withdrawAmount = -1;

            if (prices.has(lastDate)) {
                const lastPrice = prices.get(lastDate);
                withdrawAmount = historyData.lastBalance * lastPrice;
            } else {
                allPricesFound = false;
                if (!missingPriceDate) {
                    missingPriceDate = lastDate;
                    missingPriceToken = unit;
                }
            }

            return {
                investAmount: allPricesFound ? totalInvestAmount : -1,
                withdrawAmount: allPricesFound ? withdrawAmount : -1,
                priceFound: allPricesFound,
                investDate: firstDate,
                withdrawDate: lastDate,
                missingPriceDate: missingPriceDate,
                missingPriceToken: missingPriceToken
            };
        }, { investAmount: -1, withdrawAmount: -1, priceFound: false }, '计算USDT金额');
    },

    /**
     * 计算指定时间范围内的收益变化（用于日期维度的总收益列）
     * 
     * 应用统一价值公式：收益 = (结束价格 × 结束数量) - (开始价格 × 开始数量) - 追加投资价值
     * 
     * 特性：
     * - max/min确定有效时间范围
     * - 只使用历史数据，避免未来信息泄露
     * - 追加投资按开始日期价格计算成本
     * - 支持所有项目类型（Trade、DeFi、合约等）
     * 
     * @param {Object} project - 项目对象
     * @param {Map} priceMap - 价格映射
     * @param {string} startDate - 开始日期
     * @param {string} endDate - 结束日期
     * @param {string} unit - 代币单位
     * @returns {Object} 时间范围收益对象
     */
    calculateTimeRangeEarnings(project, priceMap, startDate, endDate, unit) {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            if (!window.DashboardCore.Utils.Validation.isValidProject(project)) {
                return { timeRangeEarnings: 0, totalInvestment: 0, additionalInvestment: 0, priceFound: true };
            }

            // 获取所有balance记录并按日期排序
            const balanceEntries = project.file.lists.filter(item =>
                window.DashboardCore.Utils.Validation.isValidBalanceEntry(item)
            );

            if (balanceEntries.length === 0) {
                return { timeRangeEarnings: 0, totalInvestment: 0, additionalInvestment: 0, priceFound: true };
            }

            balanceEntries.sort((a, b) => {
                const dateA = window.DashboardCore.Utils.Date.parseDate(a.date);
                const dateB = window.DashboardCore.Utils.Date.parseDate(b.date);
                if (!dateA || !dateB) return 0;
                return dateA.toMillis() - dateB.toMillis();
            });

            // 项目的实际开始和结束日期
            const projectStart = window.DashboardCore.Utils.Date.formatDate(
                window.DashboardCore.Utils.Date.parseDate(balanceEntries[0].date), 'yyyy-MM-dd'
            );
            const projectEnd = window.DashboardCore.Utils.Date.formatDate(
                window.DashboardCore.Utils.Date.parseDate(balanceEntries[balanceEntries.length - 1].date), 'yyyy-MM-dd'
            );

            // 计算有效时间范围（用户的核心逻辑）
            const effectiveStart = projectStart > startDate ? projectStart : startDate;
            const effectiveEnd = projectEnd < endDate ? projectEnd : endDate;

            // 检查项目是否在时间范围内
            if (effectiveStart > effectiveEnd) {
                return { timeRangeEarnings: 0, totalInvestment: 0, additionalInvestment: 0, priceFound: true };
            }

            // 获取有效开始日期的余额（只看历史数据）
            const startBalance = this.getBalanceAtDateBefore(balanceEntries, effectiveStart);
            
            // 获取有效结束日期的余额（只看历史数据）
            const endBalance = this.getBalanceAtDateBefore(balanceEntries, effectiveEnd);

            if (startBalance === null || endBalance === null) {
                return { timeRangeEarnings: -1, totalInvestment: -1, additionalInvestment: 0, priceFound: false };
            }

            // 计算有效时间范围内的追加投资
            let additionalInvestment = 0;
            for (const entry of balanceEntries) {
                const entryDate = window.DashboardCore.Utils.Date.formatDate(
                    window.DashboardCore.Utils.Date.parseDate(entry.date), 'yyyy-MM-dd'
                );
                
                if (entryDate >= effectiveStart && 
                    entryDate <= effectiveEnd && 
                    entry.add !== undefined && 
                    entry.add !== null) {
                    additionalInvestment += window.DashboardCore.Utils.Number.parseNumber(entry.add);
                }
            }

            // 应用统一价值公式计算时间范围收益
            // 收益 = (结束价格 × 结束数量) - (开始价格 × 开始数量) - 追加投资价值
            
            // 计算有效时间范围的投资天数
            const effectiveInvestDays = window.DashboardCore.Utils.Date.daysBetween(
                window.DashboardCore.Utils.Date.parseDate(effectiveStart),
                window.DashboardCore.Utils.Date.parseDate(effectiveEnd)
            );
            
            // 转换为USDT价值计算
            if (window.DashboardCore.isStableCoin(unit)) {
                // 稳定币：价格恒为1，直接使用数量差异
                const timeRangeEarnings = endBalance - startBalance - additionalInvestment;
                const totalInvestment = startBalance + additionalInvestment;
                const timeRangeAPR = this.calculateTimeRangeAPR(timeRangeEarnings, totalInvestment, effectiveInvestDays);
                
                return { 
                    timeRangeEarnings: timeRangeEarnings, 
                    totalInvestment: totalInvestment,
                    additionalInvestment: additionalInvestment,
                    effectiveInvestDays: effectiveInvestDays,
                    timeRangeAPR: timeRangeAPR,
                    effectiveStart: effectiveStart,
                    effectiveEnd: effectiveEnd,
                    priceFound: true 
                };
            }

            // 对于其他代币，需要价格转换以应用统一公式
            const prices = priceMap.get(unit);
            if (!prices) {
                return { timeRangeEarnings: -1, totalInvestment: -1, additionalInvestment: 0, effectiveInvestDays: 0, timeRangeAPR: 0, priceFound: false };
            }

            // 获取开始和结束日期的精确价格
            const startPriceResult = this.getPriceAtExactDate(prices, effectiveStart);
            const endPriceResult = this.getPriceAtExactDate(prices, effectiveEnd);
            
            if (!startPriceResult.found || !endPriceResult.found) {
                const missingDate = !startPriceResult.found ? effectiveStart : effectiveEnd;
                return { 
                    timeRangeEarnings: -1, 
                    totalInvestment: -1, 
                    additionalInvestment: 0, 
                    effectiveInvestDays: 0, 
                    timeRangeAPR: 0, 
                    priceFound: false,
                    missingPriceDate: missingDate,
                    missingPriceToken: unit
                };
            }

            // 应用统一价值公式
            const startValue = startBalance * startPriceResult.price;  // 开始价格 × 开始数量
            const endValue = endBalance * endPriceResult.price;        // 结束价格 × 结束数量
            const addValue = additionalInvestment * startPriceResult.price; // 追加投资按开始价格计算
            
            const timeRangeEarnings = endValue - startValue - addValue;
            const totalInvestment = startValue + addValue;
            
            // 重新计算基于USDT的APR
            const usdtTimeRangeAPR = this.calculateTimeRangeAPR(timeRangeEarnings, totalInvestment, effectiveInvestDays);
            
            return { 
                timeRangeEarnings: timeRangeEarnings, 
                totalInvestment: totalInvestment,
                additionalInvestment: addValue,  // 转换为USDT值
                effectiveInvestDays: effectiveInvestDays,
                timeRangeAPR: usdtTimeRangeAPR,  // 使用USDT基础的APR
                effectiveStart: effectiveStart,
                effectiveEnd: effectiveEnd,
                priceFound: true
            };
        }, { timeRangeEarnings: 0, totalInvestment: 0, additionalInvestment: 0, effectiveInvestDays: 0, timeRangeAPR: 0, priceFound: false }, '计算时间范围收益');
    },

    /**
     * 获取指定日期的精确价格（不进行任何插值或容差处理）
     * @param {Map} prices - 价格映射 (日期 -> 价格)
     * @param {string} targetDate - 目标日期 (YYYY-MM-DD)
     * @returns {Object} {price: number|null, found: boolean}
     */
    getPriceAtExactDate(prices, targetDate) {
        if (prices.has(targetDate)) {
            return {
                price: prices.get(targetDate),
                found: true
            };
        } else {
            return {
                price: null,
                found: false
            };
        }
    },

    /**
     * 获取指定日期之前最近的余额（优先使用精确日期匹配）
     * @param {Array} balanceEntries - 余额记录数组
     * @param {string} targetDate - 目标日期
     * @returns {number|null} 余额或null
     */
    getBalanceAtDateBefore(balanceEntries, targetDate) {
        // 只考虑目标日期之前（含当天）的记录
        const beforeEntries = balanceEntries.filter(entry => {
            const entryDate = window.DashboardCore.Utils.Date.formatDate(
                window.DashboardCore.Utils.Date.parseDate(entry.date), 'yyyy-MM-dd'
            );
            return entryDate <= targetDate;
        });

        if (beforeEntries.length === 0) {
            return null; // 无历史数据
        }

        // 优先查找精确日期匹配
        const exactMatch = beforeEntries.find(entry => {
            const entryDate = window.DashboardCore.Utils.Date.formatDate(
                window.DashboardCore.Utils.Date.parseDate(entry.date), 'yyyy-MM-dd'
            );
            return entryDate === targetDate;
        });

        if (exactMatch) {
            return window.DashboardCore.Utils.Number.parseNumber(exactMatch.balance);
        }

        // 如果没有精确匹配，返回最近的历史记录
        const latestBefore = beforeEntries[beforeEntries.length - 1];
        return window.DashboardCore.Utils.Number.parseNumber(latestBefore.balance);
    },

    /**
     * 计算时间范围内的APR
     * @param {number} earnings - 收益金额
     * @param {number} investment - 投资金额
     * @param {number} days - 投资天数
     * @returns {number} APR值
     */
    calculateTimeRangeAPR(earnings, investment, days) {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            if (investment <= 0 || days <= 0) {
                return 0;
            }

            const returnRate = earnings / investment;
            return Math.round(((returnRate / days) * 365) * 100 * 100) / 100;
        }, 0, '计算时间范围APR');
    },

    /**
     * 计算日维度收益（当日 vs 前一日的比较）
     * 
     * 日维度特殊逻辑：
     * - 当日收益 = 当日项目总价值 - 前一日项目总价值
     * - 不考虑当日追加投资，因为关注的是日间价值变化
     * - 如果前一日无数据，则收益为0
     * 
     * @param {Object} project - 项目对象
     * @param {Map} priceMap - 价格映射
     * @param {string} currentDate - 当前日期 (YYYY-MM-DD)
     * @param {string} unit - 代币单位
     * @returns {Object} 日维度收益对象
     */
    calculateDailyEarnings(project, priceMap, currentDate, unit) {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            if (!window.DashboardCore.Utils.Validation.isValidProject(project)) {
                return { dailyEarnings: 0, currentValue: 0, previousValue: 0, priceFound: true };
            }

            // 计算前一日日期
            const currentDateObj = window.DashboardCore.Utils.Date.parseDate(currentDate);
            if (!currentDateObj) {
                return { dailyEarnings: 0, currentValue: 0, previousValue: 0, priceFound: false };
            }
            
            const previousDateObj = currentDateObj.minus({ days: 1 });
            const previousDate = window.DashboardCore.Utils.Date.formatDate(previousDateObj, 'yyyy-MM-dd');

            // 获取当日和前一日的项目价值
            const currentValue = this.getProjectValueAtDate(project, priceMap, currentDate, unit);
            const previousValue = this.getProjectValueAtDate(project, priceMap, previousDate, unit);

            // 如果任一价值无法获取，返回错误状态
            if (currentValue.value === -1 || previousValue.value === -1) {
                return { 
                    dailyEarnings: -1, 
                    currentValue: currentValue.value, 
                    previousValue: previousValue.value, 
                    priceFound: false,
                    missingPriceDate: currentValue.value === -1 ? currentDate : previousDate,
                    missingPriceToken: unit
                };
            }

            // 如果前一日无项目数据（项目还未开始），收益为0
            if (previousValue.value === 0) {
                return { 
                    dailyEarnings: 0, 
                    currentValue: currentValue.value, 
                    previousValue: 0, 
                    priceFound: true 
                };
            }

            // 计算日收益
            const dailyEarnings = currentValue.value - previousValue.value;

            return { 
                dailyEarnings: dailyEarnings,
                currentValue: currentValue.value,
                previousValue: previousValue.value,
                priceFound: true
            };
        }, { dailyEarnings: 0, currentValue: 0, previousValue: 0, priceFound: false }, '计算日维度收益');
    },

    /**
     * 获取项目在指定日期的总价值
     * @param {Object} project - 项目对象
     * @param {Map} priceMap - 价格映射
     * @param {string} targetDate - 目标日期 (YYYY-MM-DD)
     * @param {string} unit - 代币单位
     * @returns {Object} {value: number, found: boolean}
     */
    getProjectValueAtDate(project, priceMap, targetDate, unit) {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            // 获取所有balance记录并按日期排序
            const balanceEntries = project.file.lists.filter(item =>
                window.DashboardCore.Utils.Validation.isValidBalanceEntry(item)
            );

            if (balanceEntries.length === 0) {
                return { value: 0, found: true };
            }

            balanceEntries.sort((a, b) => {
                const dateA = window.DashboardCore.Utils.Date.parseDate(a.date);
                const dateB = window.DashboardCore.Utils.Date.parseDate(b.date);
                if (!dateA || !dateB) return 0;
                return dateA.toMillis() - dateB.toMillis();
            });

            // 获取指定日期的余额（使用现有的获取逻辑）
            const balance = this.getBalanceAtDateBefore(balanceEntries, targetDate);
            if (balance === null) {
                return { value: 0, found: true }; // 项目还未开始
            }

            // 转换为USDT价值
            if (window.DashboardCore.isStableCoin(unit)) {
                return { value: balance, found: true };
            }

            // 对于其他代币，需要价格转换
            const prices = priceMap.get(unit);
            if (!prices) {
                return { value: -1, found: false };
            }

            const priceResult = this.getPriceAtExactDate(prices, targetDate);
            if (!priceResult.found) {
                return { value: -1, found: false };
            }

            const value = balance * priceResult.price;
            return { value: value, found: true };
        }, { value: -1, found: false }, '获取项目在指定日期的价值');
    }
};

/**
 * 衍生指标计算器
 */
window.DashboardCore.DerivedCalculator = {
    /**
     * 基于USDT价值计算APR（优化版）
     * @param {Object} usdtAmounts - USDT金额对象
     * @param {number} investDays - 投资天数
     * @returns {number} APR值
     */
    calculateUSDTBasedAPR(usdtAmounts, investDays) {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            if (!usdtAmounts || usdtAmounts.investAmount === -1 || usdtAmounts.withdrawAmount === -1 || investDays <= 0) {
                return 0;
            }

            const totalEarned = usdtAmounts.withdrawAmount - usdtAmounts.investAmount;
            const baseAmount = usdtAmounts.investAmount;

            if (baseAmount > 0) {
                const returnRate = totalEarned / baseAmount;
                return Math.round(((returnRate / investDays) * 365) * 100 * 100) / 100;
            }

            return 0;
        }, 0, '计算USDT基础APR');
    },

    /**
     * 计算仓位状态和建议操作（合并仓位等级显示）
     * @param {number} investAmount - 投资金额
     * @param {number} level - 投资等级
     * @param {Object} levelInfo - 等级信息
     * @returns {Object} 仓位状态对象
     */
    calculatePositionStatus(investAmount, level, levelInfo) {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            if (!levelInfo || !levelInfo[level]) {
                return {
                    positionStatus: "配置未找到",
                    suggestedAction: "检查配置"
                };
            }

            const limitAmount = levelInfo[level].limit || 0;
            const levelText = levelInfo[level].cn || `等级${level}`;

            if (investAmount === -1) {
                return {
                    positionStatus: `${levelText} <span style="color: #6c757d;">⚪ 价格未找到</span>`,
                    suggestedAction: "价格数据未找到"
                };
            }

            const diff = investAmount - limitAmount;
            const threshold = limitAmount * window.DashboardCore.getThreshold('POSITION', 'NORMAL_RATIO');

            if (investAmount === 0) {
                return {
                    positionStatus: `${levelText} <span style="color: #6c757d;">⚪ 未配置</span>`,
                    suggestedAction: `加仓 ${window.DashboardCore.Utils.Number.formatNumber(limitAmount)}`
                };
            } else if (Math.abs(diff) <= threshold) {
                return {
                    positionStatus: `${levelText} <span style="color: #28a745;">🟢 正常</span>`,
                    suggestedAction: "保持"
                };
            } else if (diff > threshold) {
                return {
                    positionStatus: `${levelText} <span style="color: #dc3545;">🔴 超配</span>`,
                    suggestedAction: `减仓 ${window.DashboardCore.Utils.Number.formatNumber(Math.abs(diff))}`
                };
            } else {
                return {
                    positionStatus: `${levelText} <span style="color: #ffc107;">🟡 低配</span>`,
                    suggestedAction: `加仓 ${window.DashboardCore.Utils.Number.formatNumber(Math.abs(diff))}`
                };
            }
        }, { positionStatus: "计算错误", suggestedAction: "检查数据" }, '计算仓位状态');
    },

    /**
     * 计算APR Gap
     * @param {number} currentApr - 当前APR
     * @param {number} expectApr - 期望APR
     * @param {boolean} hasInvestment - 是否有投资
     * @returns {number} APR Gap值
     */
    calculateAPRGap(currentApr, expectApr, hasInvestment = true) {
        if (!hasInvestment || expectApr === 0) return 0;
        const expectAprPercent = expectApr * 100;
        return currentApr - expectAprPercent;
    },

    /**
     * 计算组合APR
     * @param {number} currentPercent - 当前占比
     * @param {number} apr - APR值
     * @returns {number} 组合APR值
     */
    calculatePortfolioAPR(currentPercent, apr) {
        return currentPercent * apr;
    },

    /**
     * 计算总收益和日收益
     * @param {Object} usdtAmounts - USDT金额对象
     * @param {number} investDays - 投资天数
     * @returns {Object} 收益对象
     */
    calculateEarnings(usdtAmounts, investDays) {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            if (usdtAmounts.investAmount === -1 || usdtAmounts.withdrawAmount === -1) {
                return {
                    totalEarned: -1,
                    dailyEarned: -1
                };
            }

            const totalEarned = usdtAmounts.withdrawAmount - usdtAmounts.investAmount;
            const dailyEarned = investDays > 0 ? totalEarned / investDays : 0;

            return {
                totalEarned,
                dailyEarned
            };
        }, { totalEarned: -1, dailyEarned: -1 }, '计算收益');
    },

    /**
     * 计算期望收益
     * @param {number} investAmount - 投入金额
     * @param {number} expectApr - 期望APR (小数形式，如0.15表示15%)
     * @param {number} investDays - 投资天数
     * @returns {number} 期望收益金额
     */
    calculateExpectedEarnings(investAmount, expectApr, investDays) {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            if (investAmount <= 0 || expectApr <= 0 || investDays <= 0) {
                return 0;
            }

            // 期望收益 = 投入金额 * 期望APR * (投资天数 / 365)
            const expectedEarnings = investAmount * expectApr * (investDays / 365);
            return Math.round(expectedEarnings * 100) / 100;
        }, 0, '计算期望收益');
    },

    /**
     * 计算期望收益Gap
     * @param {number} actualEarnings - 实际收益
     * @param {number} expectedEarnings - 期望收益
     * @returns {number} 期望收益Gap
     */
    calculateExpectedEarningsGap(actualEarnings, expectedEarnings) {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            if (actualEarnings === -1 || expectedEarnings === 0) {
                return 0;
            }

            // 期望收益Gap = 实际收益 - 期望收益
            const gap = actualEarnings - expectedEarnings;
            return Math.round(gap * 100) / 100;
        }, 0, '计算期望收益Gap');
    }
};

/**
 * 业务逻辑处理器
 */
window.DashboardCore.BusinessLogic = {
    /**
     * 处理单个项目数据
     * @param {Object} project - 项目对象
     * @param {Object} riskInfo - 风险信息
     * @param {Object} levelInfo - 等级信息
     * @param {Map} priceMap - 价格映射
     * @returns {Object} 处理后的项目数据
     */
    processProject(project, riskInfo, levelInfo, priceMap) {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            const riskLevel = project.Risk;
            const riskText = (riskInfo[riskLevel] && riskInfo[riskLevel].text) ? riskInfo[riskLevel].text : `风险 ${riskLevel}`;
            const expectApr = (riskInfo[riskLevel] && riskInfo[riskLevel].expectApr) ? riskInfo[riskLevel].expectApr : 0;
            const unit = (project.Unit || 'USDT').toUpperCase();
            const level = project.Level || 0;

            // 获取仓位等级信息
            const levelText = (levelInfo && levelInfo[level]) ? levelInfo[level].text : `仓位 ${level}`;

            // 计算基础指标
            const historyData = window.DashboardCore.BaseCalculator.calculateProjectHistory(project, priceMap);
            const investDays = window.DashboardCore.BaseCalculator.calculateInvestDays(historyData);
            const usdtAmounts = window.DashboardCore.USDTCalculator.calculateUSDTAmount(historyData, unit, priceMap);

            // 使用优化的APR计算（基于USDT价值）
            const projectApr = window.DashboardCore.DerivedCalculator.calculateUSDTBasedAPR(usdtAmounts, investDays);

            // 计算衍生指标
            const positionInfo = window.DashboardCore.DerivedCalculator.calculatePositionStatus(usdtAmounts.investAmount, level, levelInfo);
            const earnings = window.DashboardCore.DerivedCalculator.calculateEarnings(usdtAmounts, investDays);

            // 计算债务和当前资产（新增）
            const totalDebtAmount = historyData ? historyData.totalDebtAmount : 0;
            const currentAsset = usdtAmounts.priceFound ? 
                (usdtAmounts.investAmount + earnings.totalEarned - totalDebtAmount) : -1;

            // 设置投资和提取日期
            const investDate = usdtAmounts.investDate || "";
            const withdrawDate = project.Status === "Done" ? (usdtAmounts.withdrawDate || "") : "-";

            return {
                riskLevel,
                riskText,
                expectApr,
                unit,
                level,
                levelText,
                projectApr,
                investDays,
                amountInUsdt: usdtAmounts.priceFound ? usdtAmounts.investAmount : 0,

                // 基础数据
                usdtInvestAmount: usdtAmounts.investAmount,
                usdtWithdrawAmount: usdtAmounts.withdrawAmount,
                usdtTotalEarned: earnings.totalEarned,
                usdtDailyEarned: earnings.dailyEarned,

                // 债务和当前资产（新增）
                totalDebtAmount: totalDebtAmount,
                currentAsset: currentAsset,

                // 状态信息
                positionStatus: positionInfo.positionStatus,
                suggestedAction: positionInfo.suggestedAction,

                // 价格数据状态
                priceFound: usdtAmounts.priceFound,
                
                // 缺失价格信息
                missingPriceDate: usdtAmounts.missingPriceDate,
                missingPriceToken: usdtAmounts.missingPriceToken,

                // 其他信息
                projectName: project.file.name,
                type: project.Type || "-",
                investDate,
                withdrawDate,
                status: project.Status || "Doing",
                projectLink: project.file.link
            };
        }, null, '处理项目数据');
    },

    /**
     * 创建项目详情列表
     * @param {Array} projects - 项目数组
     * @param {Object} riskInfo - 风险信息
     * @param {Object} levelInfo - 等级信息
     * @param {Map} priceMap - 价格映射
     * @returns {Array} 项目详情数组
     */
    createProtocolDetails(projects, riskInfo, levelInfo, priceMap) {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            const protocolDetails = [];

            for (const project of projects) {
                const projectData = this.processProject(project, riskInfo, levelInfo, priceMap);

                if (projectData) {
                    // 直接添加项目数据，不再按协议拆分
                    protocolDetails.push({
                        protocol: project.Protocol || "-", // 保留协议字段以兼容现有代码
                        ...projectData
                    });
                }
            }

            return protocolDetails;
        }, [], '创建项目详情列表');
    }
};

console.log('Dashboard Calculator (Complete) module loaded');
