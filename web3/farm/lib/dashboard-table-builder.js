/**
 * Dashboard 表格构建模块
 * 
 * 负责构建各种维度的表格数据：
 * - 风险维度表格
 * - 日期维度表格
 * - 表格数据汇总和计算
 * 
 * @module DashboardTableBuilder
 */

// 确保核心对象存在
window.DashboardCore = window.DashboardCore || {};

/**
 * 表格构建器
 */
window.DashboardCore.TableBuilder = {
    /**
     * 构建风险维度表格
     * @param {Object} data - 基础数据对象
     * @param {string} projectStatus - 项目状态过滤 ('Doing', 'Done', 'all')
     * @returns {Object} 表格结果对象 {headers, data}
     */
    buildRiskTable(data, projectStatus = 'Doing') {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            const { riskInfo, levelInfo, priceMap, doingProjects, allProjects } = data;
            
            // 根据状态过滤选择项目数据
            let filteredProjects;
            switch (projectStatus) {
                case 'Doing':
                    filteredProjects = doingProjects;
                    break;
                case 'Done':
                    filteredProjects = allProjects.filter(project => project.Status === 'Done');
                    break;
                case 'all':
                    filteredProjects = allProjects;
                    break;
                default:
                    filteredProjects = doingProjects;
            }
            
            if (!filteredProjects || filteredProjects.length === 0) {
                return {
                    headers: ['风险等级', '投入金额', '当前占比', '项目'],
                    data: [['暂无数据', '', '', '']]
                };
            }

            // 处理项目数据
            const protocolDetails = window.DashboardCore.BusinessLogic.createProtocolDetails(
                filteredProjects, riskInfo, levelInfo, priceMap
            );

            if (protocolDetails.length === 0) {
                return {
                    headers: ['风险等级', '投入金额', '当前占比', '项目'],
                    data: [['暂无有效数据', '', '', '']]
                };
            }

            // 计算汇总数据
            const summary = this.calculateRiskSummary(protocolDetails, riskInfo);
            const totalAmountInUsdt = Object.values(summary.amountByRisk).reduce((sum, amount) => sum + amount, 0);

            // 构建表头
            const headers = [
                '风险等级', '当前资产', '投入金额', '当前占比', '项目',
                '仓位状态', '建议操作', '总收益', '期望收益', '期望收益Gap', '投资天数', '日收益',
                '当前APR', '期望APR', 'APR Gap', '组合当前APR', '组合期望APR', '组合APR Gap'
            ];

            // 构建表格数据
            const tableData = [];
            
            // 按风险等级排序
            const sortedRiskLevels = Object.keys(riskInfo).sort((a, b) => parseInt(a) - parseInt(b));

            for (const riskLevel of sortedRiskLevels) {
                // 添加风险等级汇总行
                const summaryRow = this.buildRiskSummaryRow(riskLevel, riskInfo, summary, totalAmountInUsdt);
                tableData.push(summaryRow);

                // 添加该风险等级下的项目详情行
                const projectsInThisRisk = protocolDetails.filter(p => p.riskLevel == riskLevel);
                
                // 按项目名称排序
                projectsInThisRisk.sort((a, b) => {
                    const nameA = a.projectLink && a.projectLink.path ? a.projectLink.path.split('/').pop() : '';
                    const nameB = b.projectLink && b.projectLink.path ? b.projectLink.path.split('/').pop() : '';
                    return nameA.localeCompare(nameB);
                });

                for (const project of projectsInThisRisk) {
                    const projectRow = this.buildProjectRow(project, totalAmountInUsdt);
                    tableData.push(projectRow);
                }
            }

            // 添加总计行
            const totalRow = this.buildTotalRow(summary, riskInfo, totalAmountInUsdt);
            tableData.push(totalRow);

            return {
                headers,
                data: tableData
            };
        }, { headers: ['错误'], data: [['构建表格时出错']] }, '构建风险维度表格');
    },

    /**
     * 计算风险等级汇总数据
     * @param {Array} protocolDetails - 项目详情数组
     * @param {Object} riskInfo - 风险信息
     * @returns {Object} 汇总数据对象
     */
    calculateRiskSummary(protocolDetails, riskInfo) {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            const amountByRisk = {};
            const currentAssetByRisk = {};  // 新增：当前资产汇总
            const aprByRisk = {};
            const expectAprByRisk = {};
            const totalEarnedByRisk = {};
            const expectedEarningsByRisk = {};
            const expectedEarningsGapByRisk = {};
            const maxInvestDaysByRisk = {};
            const dailyEarnedByRisk = {};

            // 初始化所有风险等级
            for (const riskLevel in riskInfo) {
                amountByRisk[riskLevel] = 0;
                currentAssetByRisk[riskLevel] = 0;  // 新增：初始化当前资产
                aprByRisk[riskLevel] = 0;
                expectAprByRisk[riskLevel] = riskInfo[riskLevel].expectApr || 0;
                totalEarnedByRisk[riskLevel] = 0;
                expectedEarningsByRisk[riskLevel] = 0;
                expectedEarningsGapByRisk[riskLevel] = 0;
                maxInvestDaysByRisk[riskLevel] = 0;
                dailyEarnedByRisk[riskLevel] = 0;
            }

            // 计算各风险等级的投入总额和收益数据
            for (const detail of protocolDetails) {
                if (amountByRisk[detail.riskLevel] !== undefined) {
                    amountByRisk[detail.riskLevel] += detail.amountInUsdt;

                    // 累计当前资产（新增）
                    if (detail.currentAsset !== -1) {
                        currentAssetByRisk[detail.riskLevel] += detail.currentAsset;
                    }

                    // 累计总收益（只有价格数据可用的项目）
                    if (detail.usdtTotalEarned !== -1) {
                        totalEarnedByRisk[detail.riskLevel] += detail.usdtTotalEarned;
                    }

                    // 计算期望收益
                    if (detail.amountInUsdt > 0) {
                        const expectedEarnings = window.DashboardCore.DerivedCalculator.calculateExpectedEarnings(
                            detail.amountInUsdt, detail.expectApr, detail.investDays
                        );
                        expectedEarningsByRisk[detail.riskLevel] += expectedEarnings;

                        // 计算期望收益Gap
                        if (detail.usdtTotalEarned !== -1) {
                            const expectedGap = window.DashboardCore.DerivedCalculator.calculateExpectedEarningsGap(
                                detail.usdtTotalEarned, expectedEarnings
                            );
                            expectedEarningsGapByRisk[detail.riskLevel] += expectedGap;
                        }
                    }

                    // 记录最大投资天数
                    if (detail.investDays > maxInvestDaysByRisk[detail.riskLevel]) {
                        maxInvestDaysByRisk[detail.riskLevel] = detail.investDays;
                    }

                    // 累计日收益（只有价格数据可用的项目）
                    if (detail.usdtDailyEarned !== -1) {
                        dailyEarnedByRisk[detail.riskLevel] += detail.usdtDailyEarned;
                    }
                }
            }

            // 计算各风险等级的简单平均APR
            for (const riskLevel in amountByRisk) {
                const projectsInRisk = protocolDetails.filter(p => p.riskLevel == riskLevel);
                if (projectsInRisk.length > 0) {
                    const averageApr = projectsInRisk.reduce((sum, p) => sum + p.projectApr, 0) / projectsInRisk.length;
                    aprByRisk[riskLevel] = Math.round(averageApr * 100) / 100;
                }
            }

            return {
                amountByRisk,
                currentAssetByRisk,  // 新增：当前资产汇总
                aprByRisk,
                expectAprByRisk,
                totalEarnedByRisk,
                expectedEarningsByRisk,
                expectedEarningsGapByRisk,
                maxInvestDaysByRisk,
                dailyEarnedByRisk
            };
        }, {}, '计算风险等级汇总');
    },

    /**
     * 构建风险等级汇总行
     * @param {string} riskLevel - 风险等级
     * @param {Object} riskInfo - 风险信息
     * @param {Object} summary - 汇总数据
     * @param {number} totalAmountInUsdt - 总投入金额
     * @returns {Array} 表格行数据
     */
    buildRiskSummaryRow(riskLevel, riskInfo, summary, totalAmountInUsdt) {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            const invested = summary.amountByRisk[riskLevel] || 0;
            const currentAsset = summary.currentAssetByRisk[riskLevel] || 0;  // 新增：当前资产
            const currentPercent = totalAmountInUsdt > 0 ? (invested / totalAmountInUsdt) : 0;
            const limitPercent = riskInfo[riskLevel].percent;
            const currentApr = summary.aprByRisk[riskLevel] || 0;
            const expectApr = summary.expectAprByRisk[riskLevel] || 0;

            // 获取聚合的收益数据
            const totalEarned = summary.totalEarnedByRisk[riskLevel] || 0;
            const expectedEarnings = summary.expectedEarningsByRisk[riskLevel] || 0;
            const expectedEarningsGap = summary.expectedEarningsGapByRisk[riskLevel] || 0;
            const maxInvestDays = summary.maxInvestDaysByRisk[riskLevel] || 0;
            const dailyEarned = summary.dailyEarnedByRisk[riskLevel] || 0;

            // 计算各种APR指标
            const expectAprPercent = expectApr * 100;
            const aprGap = (invested > 0 && expectAprPercent > 0) ?
                window.DashboardCore.DerivedCalculator.calculateAPRGap(currentApr, expectApr, true) : 0;
            const portfolioCurrentApr = window.DashboardCore.DerivedCalculator.calculatePortfolioAPR(currentPercent, currentApr);
            const portfolioExpectApr = window.DashboardCore.DerivedCalculator.calculatePortfolioAPR(currentPercent, expectAprPercent);
            const portfolioAprGap = portfolioCurrentApr - portfolioExpectApr;

            // 计算状态文本
            let statusText;
            if (invested === 0) {
                statusText = `⚪ 未配置 (${(limitPercent * 100).toFixed(1)}%)`;
            } else {
                const diff = currentPercent - limitPercent;
                if (diff > 0.05) {
                    statusText = `🔴 超配 (${(limitPercent * 100).toFixed(1)}%)`;
                } else if (diff < -0.05) {
                    statusText = `🟡 低配 (${(limitPercent * 100).toFixed(1)}%)`;
                } else {
                    statusText = `🟢 正常 (${(limitPercent * 100).toFixed(1)}%)`;
                }
            }

            // 构建合并后的风险等级+健康状态文本
            let mergedRiskText;
            
            // 风险等级颜色逻辑
            const risk = parseInt(riskLevel);
            let riskColor = window.DashboardCore.getColor('VALUE', 'NEUTRAL');
            if (risk <= 1) {
                riskColor = window.DashboardCore.getColor('RISK', 'LOW');
            } else if (risk === 2) {
                riskColor = window.DashboardCore.getColor('RISK', 'MEDIUM');
            } else if (risk >= 3) {
                riskColor = window.DashboardCore.getColor('RISK', 'HIGH');
            }
            
            // 健康状态颜色逻辑
            let statusColor = '#6c757d'; // 默认灰色
            if (invested === 0) {
                statusColor = '#6c757d'; // 未配置：灰色
            } else {
                const diff = currentPercent - limitPercent;
                if (diff > 0.05) {
                    statusColor = '#dc3545'; // 超配：红色
                } else if (diff < -0.05) {
                    statusColor = '#ffc107'; // 低配：黄色
                } else {
                    statusColor = '#28a745'; // 正常：绿色
                }
            }
            
            // 构建合并文本：风险等级用风险颜色，健康状态用状态颜色
            mergedRiskText = `<span style="color: ${riskColor}; font-weight: bold;">${riskInfo[riskLevel].text}</span> <span style="color: ${statusColor}; font-weight: bold;">${statusText}</span>`;

            return [
                mergedRiskText, // 风险等级 + 健康状态合并
                // 当前资产（新增，正常显示）
                currentAsset !== -1 ? 
                    `${window.DashboardCore.Utils.Number.formatNumber(currentAsset)}` : 
                    "价格数据未找到",
                `**${window.DashboardCore.Utils.Number.formatNumber(invested)}**`, // 投入金额（加粗）
                `**${window.DashboardCore.Utils.Number.formatPercentage(currentPercent * 100)}**`, // 当前占比（加粗）
                "", // 项目列为空
                "", // 仓位状态（聚合行为空）
                "", // 建议操作（聚合行为空）
                // 总收益（带颜色和粗体）
                totalEarned !== 0 ?
                    (totalEarned > 0 ?
                        `<span style="color: #28a745; font-weight: bold;">${window.DashboardCore.Utils.Number.formatNumber(totalEarned)}</span>` :
                        `<span style="color: #dc3545; font-weight: bold;">${window.DashboardCore.Utils.Number.formatNumber(totalEarned)}</span>`) :
                    "**0**",
                // 期望收益（加粗）
                expectedEarnings > 0 ? `**${window.DashboardCore.Utils.Number.formatNumber(expectedEarnings)}**` : "**0**",
                // 期望收益Gap（带颜色和粗体）
                expectedEarningsGap !== 0 ?
                    (expectedEarningsGap > 0 ?
                        `<span style="color: #28a745; font-weight: bold;">${window.DashboardCore.Utils.Number.formatNumber(expectedEarningsGap)}</span>` :
                        `<span style="color: #dc3545; font-weight: bold;">${window.DashboardCore.Utils.Number.formatNumber(expectedEarningsGap)}</span>`) :
                    "**0**",
                maxInvestDays > 0 ? `**${maxInvestDays.toFixed(2)}**` : "**0**", // 投资天数（加粗）
                // 日收益（带颜色和粗体）
                dailyEarned !== 0 ?
                    (dailyEarned > 0 ?
                        `<span style="color: #28a745; font-weight: bold;">${window.DashboardCore.Utils.Number.formatNumber(dailyEarned)}</span>` :
                        `<span style="color: #dc3545; font-weight: bold;">${window.DashboardCore.Utils.Number.formatNumber(dailyEarned)}</span>`) :
                    "**0**",
                // APR相关列
                // 当前APR（带颜色和粗体）
                currentApr > 0 ?
                    `<span style="color: #28a745; font-weight: bold;">${window.DashboardCore.Utils.Number.formatPercentage(currentApr)}</span>` :
                    currentApr < 0 ?
                        `<span style="color: #dc3545; font-weight: bold;">${window.DashboardCore.Utils.Number.formatPercentage(currentApr)}</span>` :
                        `**${window.DashboardCore.Utils.Number.formatPercentage(currentApr)}**`,
                invested > 0 && expectApr > 0 ? `**${window.DashboardCore.Utils.Number.formatPercentage(expectAprPercent, 0)}**` : "**0%**", // 期望APR（加粗）
                invested > 0 && expectApr > 0 ?
                    window.DashboardCore.Utils.Color.getAPRGapColoredText(aprGap).replace('<span style="color:', '<span style="color:').replace(';">', '; font-weight: bold;">') :
                    "**0%**", // APR Gap（加粗）
                // 当前APR总比（带颜色和粗体）
                portfolioCurrentApr > 0 ?
                    `<span style="color: #28a745; font-weight: bold;">${window.DashboardCore.Utils.Number.formatPercentage(portfolioCurrentApr)}</span>` :
                    portfolioCurrentApr < 0 ?
                        `<span style="color: #dc3545; font-weight: bold;">${window.DashboardCore.Utils.Number.formatPercentage(portfolioCurrentApr)}</span>` :
                        `**${window.DashboardCore.Utils.Number.formatPercentage(portfolioCurrentApr)}**`,
                `**${window.DashboardCore.Utils.Number.formatPercentage(portfolioExpectApr)}**`, // 期望APR总比（加粗）
                invested > 0 && expectApr > 0 ?
                    window.DashboardCore.Utils.Color.getAPRGapColoredText(portfolioAprGap).replace('<span style="color:', '<span style="color:').replace(';">', '; font-weight: bold;">') :
                    "**0%**" // APR Gap总比（加粗）
            ];
        }, [], '构建风险等级汇总行');
    },

    /**
     * 构建项目详情行
     * @param {Object} project - 项目数据
     * @param {number} totalAmountInUsdt - 总投入金额
     * @returns {Array} 表格行数据
     */
    buildProjectRow(project, totalAmountInUsdt) {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            const projectPercent = totalAmountInUsdt > 0 ? (project.amountInUsdt / totalAmountInUsdt) : 0;
            const projectExpectApr = project.expectApr * 100;
            const projectAprGap = window.DashboardCore.DerivedCalculator.calculateAPRGap(project.projectApr, project.expectApr, project.amountInUsdt > 0);
            const projectPortfolioCurrentApr = window.DashboardCore.DerivedCalculator.calculatePortfolioAPR(projectPercent, project.projectApr);
            const projectPortfolioExpectApr = window.DashboardCore.DerivedCalculator.calculatePortfolioAPR(projectPercent, projectExpectApr);
            const projectPortfolioAprGap = projectPortfolioCurrentApr - projectPortfolioExpectApr;

            // 计算项目的期望收益和期望收益Gap
            const expectedEarnings = window.DashboardCore.DerivedCalculator.calculateExpectedEarnings(
                project.amountInUsdt, project.expectApr, project.investDays
            );
            const expectedEarningsGap = window.DashboardCore.DerivedCalculator.calculateExpectedEarningsGap(
                project.usdtTotalEarned, expectedEarnings
            );

            return [
                "", // 风险等级列为空
                // 当前资产（新增，正常显示，带债务提示）
                project.currentAsset === -1 ? 
                    (project.missingPriceDate && project.missingPriceToken ? 
                        `${project.missingPriceToken} ${project.missingPriceDate} 价格数据未找到` : 
                        "价格数据未找到") :
                    (() => {
                        let assetDisplay = window.DashboardCore.Utils.Number.formatNumber(project.currentAsset);
                        // 如果有债务，添加债务提示
                        if (project.totalDebtAmount > 0) {
                            assetDisplay += ` <span style="color: #dc3545; font-size: 0.9em;">(债务: ${window.DashboardCore.Utils.Number.formatNumber(project.totalDebtAmount)})</span>`;
                        }
                        return assetDisplay;
                    })(),
                window.DashboardCore.Utils.Number.formatNumber(project.amountInUsdt),
                window.DashboardCore.Utils.Number.formatPercentage(projectPercent * 100),
                project.projectLink,
                // 调整顺序：仓位状态、建议操作、总收益、期望收益、期望收益Gap、投资天数、日收益
                project.positionStatus || "-",
                project.suggestedAction || "-",
                project.usdtTotalEarned === -1 ? 
                    (project.missingPriceDate && project.missingPriceToken ? 
                        `${project.missingPriceToken} ${project.missingPriceDate} 价格数据未找到` : 
                        "价格数据未找到") :
                    (project.usdtTotalEarned !== 0 ? window.DashboardCore.Utils.Color.formatColoredNumber(project.usdtTotalEarned) : "0"), // 总收益（带颜色）
                // 期望收益
                expectedEarnings > 0 ? window.DashboardCore.Utils.Number.formatNumber(expectedEarnings) : "0",
                // 期望收益Gap（带颜色）
                project.usdtTotalEarned === -1 ? 
                    (project.missingPriceDate && project.missingPriceToken ? 
                        `${project.missingPriceToken} ${project.missingPriceDate} 价格数据未找到` : 
                        "价格数据未找到") :
                    (expectedEarningsGap !== 0 ? window.DashboardCore.Utils.Color.formatColoredNumber(expectedEarningsGap) : "0"),
                project.investDays > 0 ? `${project.investDays.toFixed(2)}` : "0",
                project.usdtDailyEarned === -1 ? 
                    (project.missingPriceDate && project.missingPriceToken ? 
                        `${project.missingPriceToken} ${project.missingPriceDate} 价格数据未找到` : 
                        "价格数据未找到") :
                    (project.usdtDailyEarned !== 0 ? window.DashboardCore.Utils.Color.formatColoredNumber(project.usdtDailyEarned) : "0"), // 日收益（带颜色）
                // APR相关列
                window.DashboardCore.Utils.Color.formatColoredPercentage(project.projectApr), // 当前APR（带颜色）
                project.amountInUsdt > 0 && projectExpectApr > 0 ? window.DashboardCore.Utils.Number.formatPercentage(projectExpectApr, 0) : "0%",
                project.amountInUsdt > 0 && projectExpectApr > 0 ? window.DashboardCore.Utils.Color.getAPRGapColoredText(projectAprGap) : "0%",
                window.DashboardCore.Utils.Color.formatColoredPercentage(projectPortfolioCurrentApr), // 当前APR总比（带颜色）
                window.DashboardCore.Utils.Number.formatPercentage(projectPortfolioExpectApr),
                project.amountInUsdt > 0 && projectExpectApr > 0 ? window.DashboardCore.Utils.Color.getAPRGapColoredText(projectPortfolioAprGap) : "0%"
            ];
        }, [], '构建项目详情行');
    },

    /**
     * 构建总计行
     * @param {Object} summary - 汇总数据
     * @param {Object} riskInfo - 风险信息
     * @param {number} totalAmountInUsdt - 总投入金额
     * @returns {Array} 表格行数据
     */
    buildTotalRow(summary, riskInfo, totalAmountInUsdt) {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            // 计算总计数据
            const totalCurrentAsset = Object.values(summary.currentAssetByRisk).reduce((sum, asset) => sum + asset, 0);  // 新增：总当前资产
            const totalTotalEarned = Object.values(summary.totalEarnedByRisk).reduce((sum, earned) => sum + earned, 0);
            const totalExpectedEarnings = Object.values(summary.expectedEarningsByRisk).reduce((sum, expected) => sum + expected, 0);
            const totalExpectedEarningsGap = Object.values(summary.expectedEarningsGapByRisk).reduce((sum, gap) => sum + gap, 0);
            const totalDailyEarned = Object.values(summary.dailyEarnedByRisk).reduce((sum, daily) => sum + daily, 0);
            const maxTotalInvestDays = Math.max(...Object.values(summary.maxInvestDaysByRisk));

            // 计算有项目的风险等级的平均APR
            const riskLevelsWithProjects = Object.keys(summary.amountByRisk).filter(level => summary.amountByRisk[level] > 0);
            const totalCurrentApr = riskLevelsWithProjects.length > 0 ?
                riskLevelsWithProjects.reduce((sum, level) => sum + summary.aprByRisk[level], 0) / riskLevelsWithProjects.length : 0;
            const totalExpectApr = riskLevelsWithProjects.length > 0 ?
                riskLevelsWithProjects.reduce((sum, level) => sum + (summary.expectAprByRisk[level] * 100), 0) / riskLevelsWithProjects.length : 0;

            const totalAprGap = totalCurrentApr - totalExpectApr;

            // 组合APR总比就是当前APR和期望APR（因为总占比是100%）
            const totalPortfolioCurrentApr = totalCurrentApr;
            const totalPortfolioExpectApr = totalExpectApr;
            const totalPortfolioAprGap = totalAprGap;

            return [
                `**总计**`, // 总计标识（加粗）
                // 总当前资产（新增，正常显示）
                `**${window.DashboardCore.Utils.Number.formatNumber(totalCurrentAsset)}**`,
                `**${window.DashboardCore.Utils.Number.formatNumber(totalAmountInUsdt)}**`, // 总投入金额（加粗）
                `**100%**`, // 总占比（加粗）
                "", // 项目列为空
                "", // 仓位状态（总计行为空）
                "", // 建议操作（总计行为空）
                // 总收益（带颜色和粗体）
                totalTotalEarned !== 0 ?
                    (totalTotalEarned > 0 ?
                        `<span style="color: #28a745; font-weight: bold;">${window.DashboardCore.Utils.Number.formatNumber(totalTotalEarned)}</span>` :
                        `<span style="color: #dc3545; font-weight: bold;">${window.DashboardCore.Utils.Number.formatNumber(totalTotalEarned)}</span>`) :
                    "**0**",
                // 期望收益（加粗）
                totalExpectedEarnings > 0 ? `**${window.DashboardCore.Utils.Number.formatNumber(totalExpectedEarnings)}**` : "**0**",
                // 期望收益Gap（带颜色和粗体）
                totalExpectedEarningsGap !== 0 ?
                    (totalExpectedEarningsGap > 0 ?
                        `<span style="color: #28a745; font-weight: bold;">${window.DashboardCore.Utils.Number.formatNumber(totalExpectedEarningsGap)}</span>` :
                        `<span style="color: #dc3545; font-weight: bold;">${window.DashboardCore.Utils.Number.formatNumber(totalExpectedEarningsGap)}</span>`) :
                    "**0**",
                maxTotalInvestDays > 0 ? `**${maxTotalInvestDays.toFixed(2)}**` : "**0**", // 投资天数（加粗）
                // 日收益（带颜色和粗体）
                totalDailyEarned !== 0 ?
                    (totalDailyEarned > 0 ?
                        `<span style="color: #28a745; font-weight: bold;">${window.DashboardCore.Utils.Number.formatNumber(totalDailyEarned)}</span>` :
                        `<span style="color: #dc3545; font-weight: bold;">${window.DashboardCore.Utils.Number.formatNumber(totalDailyEarned)}</span>`) :
                    "**0**",
                // APR相关列
                // 当前APR（带颜色和粗体）
                totalCurrentApr > 0 ?
                    `<span style="color: #28a745; font-weight: bold;">${window.DashboardCore.Utils.Number.formatPercentage(totalCurrentApr)}</span>` :
                    totalCurrentApr < 0 ?
                        `<span style="color: #dc3545; font-weight: bold;">${window.DashboardCore.Utils.Number.formatPercentage(totalCurrentApr)}</span>` :
                        `**${window.DashboardCore.Utils.Number.formatPercentage(totalCurrentApr)}**`,
                `**${window.DashboardCore.Utils.Number.formatPercentage(totalExpectApr, 0)}**`, // 期望APR（加粗）
                window.DashboardCore.Utils.Color.getAPRGapColoredText(totalAprGap).replace('<span style="color:', '<span style="color:').replace(';">', '; font-weight: bold;">'), // APR Gap（加粗）
                // 当前APR总比（带颜色和粗体）
                totalPortfolioCurrentApr > 0 ?
                    `<span style="color: #28a745; font-weight: bold;">${window.DashboardCore.Utils.Number.formatPercentage(totalPortfolioCurrentApr)}</span>` :
                    totalPortfolioCurrentApr < 0 ?
                        `<span style="color: #dc3545; font-weight: bold;">${window.DashboardCore.Utils.Number.formatPercentage(totalPortfolioCurrentApr)}</span>` :
                        `**${window.DashboardCore.Utils.Number.formatPercentage(totalPortfolioCurrentApr)}**`,
                `**${window.DashboardCore.Utils.Number.formatPercentage(totalPortfolioExpectApr)}**`, // 期望APR总比（加粗）
                window.DashboardCore.Utils.Color.getAPRGapColoredText(totalPortfolioAprGap).replace('<span style="color:', '<span style="color:').replace(';">', '; font-weight: bold;">') // APR Gap总比（加粗）
            ];
        }, [], '构建总计行');
    }
};

/**
 * 日期数据聚合器
 * 负责按不同时间粒度聚合项目数据
 */
window.DashboardCore.DateAggregator = {
    /**
     * 按项目提取日期数据
     */
    extractProjectDateData(projects) {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            const projectDateData = [];

            for (const project of projects) {
                if (!project.file || !project.file.lists || !Array.isArray(project.file.lists)) {
                    continue;
                }

                const balanceEntries = project.file.lists.filter(item =>
                    item &&
                    item.balance !== undefined &&
                    item.date !== undefined &&
                    item.date !== null &&
                    item.date.toString().trim() !== ""
                );

                if (balanceEntries.length === 0) continue;

                // 按日期排序
                balanceEntries.sort((a, b) => {
                    try {
                        const dateA = window.DashboardCore.Utils.Date.parseDate(a.date);
                        const dateB = window.DashboardCore.Utils.Date.parseDate(b.date);
                        if (!dateA || !dateB) return 0;
                        return dateA.toMillis() - dateB.toMillis();
                    } catch (e) {
                        return 0;
                    }
                });

                // 为每个日期条目添加项目信息
                for (const entry of balanceEntries) {
                    try {
                        const dateObj = window.DashboardCore.Utils.Date.parseDate(entry.date);
                        if (!dateObj) continue;

                        const dateStr = window.DashboardCore.Utils.Date.formatDate(dateObj, 'yyyy-MM-dd');

                        projectDateData.push({
                            project: project,
                            date: dateStr,
                            dateObj: dateObj,
                            balance: window.DashboardCore.Utils.Number.parseNumber(entry.balance),
                            add: entry.add ? window.DashboardCore.Utils.Number.parseNumber(entry.add) : 0,
                            remark: entry.remark || ""
                        });
                    } catch (e) {
                        console.warn('处理日期条目时出错:', e);
                    }
                }
            }

            return projectDateData;
        }, [], '提取项目日期数据');
    },

    /**
     * 按月聚合数据
     */
    aggregateByMonth(projectDateData) {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            const monthlyGroups = new Map();

            for (const item of projectDateData) {
                const monthKey = item.dateObj.toFormat("yyyy-MM");
                if (!monthlyGroups.has(monthKey)) {
                    monthlyGroups.set(monthKey, {
                        timeKey: monthKey,
                        displayText: item.dateObj.toFormat("yyyy年M月"),
                        projectData: []
                    });
                }
                monthlyGroups.get(monthKey).projectData.push(item);
            }

            return Array.from(monthlyGroups.values()).sort((a, b) => b.timeKey.localeCompare(a.timeKey));
        }, [], '按月聚合数据');
    },

    /**
     * 按周聚合数据
     */
    aggregateByWeek(projectDateData) {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            const weeklyGroups = new Map();

            for (const item of projectDateData) {
                const weekStart = item.dateObj.startOf('week');
                const weekEnd = item.dateObj.endOf('week');
                const weekKey = weekStart.toFormat("yyyy-'W'WW");

                if (!weeklyGroups.has(weekKey)) {
                    weeklyGroups.set(weekKey, {
                        timeKey: weekKey,
                        displayText: `${weekStart.toFormat("yyyy年M月d日")} - ${weekEnd.toFormat("M月d日")}`,
                        weekStart: weekStart.toFormat("yyyy-MM-dd"),
                        weekEnd: weekEnd.toFormat("yyyy-MM-dd"),
                        projectData: []
                    });
                }
                weeklyGroups.get(weekKey).projectData.push(item);
            }

            return Array.from(weeklyGroups.values()).sort((a, b) => b.timeKey.localeCompare(a.timeKey));
        }, [], '按周聚合数据');
    },

    /**
     * 按年聚合数据
     */
    aggregateByYear(projectDateData) {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            const yearlyGroups = new Map();

            for (const item of projectDateData) {
                const yearKey = item.dateObj.toFormat("yyyy");
                if (!yearlyGroups.has(yearKey)) {
                    yearlyGroups.set(yearKey, {
                        timeKey: yearKey,
                        displayText: `${yearKey}年`,
                        projectData: []
                    });
                }
                yearlyGroups.get(yearKey).projectData.push(item);
            }

            return Array.from(yearlyGroups.values()).sort((a, b) => b.timeKey.localeCompare(a.timeKey));
        }, [], '按年聚合数据');
    },

    /**
     * 按日聚合数据
     */
    aggregateByDay(projectDateData) {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            const dailyGroups = new Map();

            for (const item of projectDateData) {
                const dateKey = item.date;
                if (!dailyGroups.has(dateKey)) {
                    const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
                    const weekday = weekdays[item.dateObj.weekday % 7];
                    dailyGroups.set(dateKey, {
                        timeKey: dateKey,
                        displayText: `${item.dateObj.toFormat("yyyy年M月d日")} (${weekday})`,
                        projectData: []
                    });
                }
                dailyGroups.get(dateKey).projectData.push(item);
            }

            return Array.from(dailyGroups.values()).sort((a, b) => b.timeKey.localeCompare(a.timeKey));
        }, [], '按日聚合数据');
    }
};

/**
 * 日期维度表格构建器
 * 负责构建日期维度的表格数据
 */
window.DashboardCore.TableBuilder.buildDateTable = function(data, granularity = 'monthly') {
    return window.DashboardCore.Utils.Error.safeExecute(() => {
        const { allProjects, riskInfo, levelInfo, priceMap } = data;

        // 数据验证
        if (!allProjects || allProjects.length === 0) {
            return {
                headers: ['时间', '项目数', '总投入', '总收益', '平均APR'],
                data: []
            };
        }

        // 1. 提取项目日期数据
        const projectDateData = window.DashboardCore.DateAggregator.extractProjectDateData(allProjects);
        if (projectDateData.length === 0) {
            console.info('日期维度表格构建：未找到有效的日期数据');
            return {
                headers: ['时间', '项目数', '总投入', '总收益', '平均APR'],
                data: []
            };
        }

        // 2. 根据粒度聚合项目日期数据
        let timeGroups = [];
        switch (granularity) {
            case 'daily':
                timeGroups = window.DashboardCore.DateAggregator.aggregateByDay(projectDateData);
                break;
            case 'weekly':
                timeGroups = window.DashboardCore.DateAggregator.aggregateByWeek(projectDateData);
                break;
            case 'monthly':
                timeGroups = window.DashboardCore.DateAggregator.aggregateByMonth(projectDateData);
                break;
            case 'yearly':
                timeGroups = window.DashboardCore.DateAggregator.aggregateByYear(projectDateData);
                break;
            default:
                timeGroups = window.DashboardCore.DateAggregator.aggregateByMonth(projectDateData);
        }

        if (timeGroups.length === 0) {
            // 根据粒度调整第一列标题
            const firstColumnTitle = granularity === 'daily' ? '日期' :
                                   granularity === 'weekly' ? '周' :
                                   granularity === 'monthly' ? '月份' : '年份';
            return {
                headers: [
                    firstColumnTitle,
                    '投入金额',
                    '当前占比',
                    '健康程度',
                    '项目',
                    '状态',
                    '总收益',
                    '期望收益',
                    '期望收益Gap',
                    '投资天数',
                    '日收益',
                    '当前APR',
                    '期望APR',
                    'APR Gap',
                    '当前APR总比',
                    '期望APR总比',
                    'APR Gap总比'
                ],
                data: []
            };
        }

        // 3. 构建表格数据
        const tableData = [];

        // 根据粒度调整第一列标题
        const firstColumnTitle = granularity === 'daily' ? '日期' :
                               granularity === 'weekly' ? '周' :
                               granularity === 'monthly' ? '月份' : '年份';

        const headers = [
            firstColumnTitle,
            '投入金额',
            '当前占比',
            '健康程度',
            '项目',
            '状态',
            '总收益',
            '期望收益',
            '期望收益Gap',
            '投资天数',
            '日收益',
            '当前APR',
            '期望APR',
            'APR Gap',
            '当前APR总比',
            '期望APR总比',
            'APR Gap总比'
        ];

            // 处理每个时间组
            for (const timeGroup of timeGroups) {
                // 获取该时间段内的所有项目
                const projectsInTimeGroup = new Map();

                for (const item of timeGroup.projectData) {
                    const projectKey = item.project.file.name;
                    if (!projectsInTimeGroup.has(projectKey)) {
                        projectsInTimeGroup.set(projectKey, {
                            project: item.project,
                            dateEntries: []
                        });
                    }
                    projectsInTimeGroup.get(projectKey).dateEntries.push(item);
                }

                // 计算时间范围
                let timeRange = null;
                if (timeGroup.projectData.length > 0) {
                    const dates = timeGroup.projectData.map(item => item.date).sort();

                    // 根据粒度调整时间范围
                    switch (granularity) {
                        case 'monthly':
                            // 月粒度：使用月份的第一天和最后一天
                            const monthStart = window.DashboardCore.Utils.Date.parseDate(dates[0]).startOf('month');
                            const monthEnd = window.DashboardCore.Utils.Date.parseDate(dates[0]).endOf('month');
                            timeRange = {
                                startDate: window.DashboardCore.Utils.Date.formatDate(monthStart, 'yyyy-MM-dd'),
                                endDate: window.DashboardCore.Utils.Date.formatDate(monthEnd, 'yyyy-MM-dd')
                            };
                            break;
                        case 'weekly':
                            // 周粒度：使用周的开始和结束
                            if (timeGroup.weekStart && timeGroup.weekEnd) {
                                timeRange = {
                                    startDate: timeGroup.weekStart,
                                    endDate: timeGroup.weekEnd
                                };
                            }
                            break;
                        case 'yearly':
                            // 年粒度：使用年份的第一天和最后一天
                            const yearStart = window.DashboardCore.Utils.Date.parseDate(dates[0]).startOf('year');
                            const yearEnd = window.DashboardCore.Utils.Date.parseDate(dates[0]).endOf('year');
                            timeRange = {
                                startDate: window.DashboardCore.Utils.Date.formatDate(yearStart, 'yyyy-MM-dd'),
                                endDate: window.DashboardCore.Utils.Date.formatDate(yearEnd, 'yyyy-MM-dd')
                            };
                            break;
                        case 'daily':
                        default:
                            // 日粒度：保持原有逻辑
                            timeRange = {
                                startDate: dates[0],
                                endDate: dates[dates.length - 1]
                            };
                            break;
                    }
                }

                // 处理该时间段内的每个项目
                const protocolDetails = [];
                for (const [projectKey, projectInfo] of projectsInTimeGroup) {
                    const project = projectInfo.project;

                    // 计算该项目在该时间段的数据
                    let historyData;
                    if (timeRange) {
                        historyData = window.DashboardCore.BaseCalculator.calculateProjectHistoryInTimeRange(
                            project, priceMap, timeRange.startDate, timeRange.endDate
                        );
                    } else {
                        historyData = window.DashboardCore.BaseCalculator.calculateProjectHistory(project, priceMap);
                    }

                    if (!historyData) continue;

                    const unit = (project.Unit || 'USDT').toUpperCase();
                    const riskLevel = project.Risk || 0;
                    const expectApr = (riskInfo[riskLevel] && riskInfo[riskLevel].expectApr) ? riskInfo[riskLevel].expectApr : 0;
                    const investDays = window.DashboardCore.BaseCalculator.calculateInvestDaysForDateView(project, timeRange?.endDate || new Date().toISOString().split('T')[0]);
                    const usdtAmounts = window.DashboardCore.USDTCalculator.calculateUSDTAmount(historyData, unit, priceMap);

                    // 计算时间范围收益（日维度使用特殊逻辑）
                    let timeRangeResult = { 
                        timeRangeEarnings: 0, 
                        totalInvestment: 0, 
                        additionalInvestment: 0, 
                        effectiveInvestDays: 0,
                        timeRangeAPR: 0,
                        priceFound: true 
                    };
                    
                    if (timeRange) {
                        if (granularity === 'daily') {
                            // 日维度：使用当日 vs 前一日的比较逻辑
                            const dailyResult = window.DashboardCore.USDTCalculator.calculateDailyEarnings(
                                project, priceMap, timeRange.endDate, unit
                            );
                            
                            timeRangeResult = {
                                timeRangeEarnings: dailyResult.dailyEarnings,
                                totalInvestment: dailyResult.currentValue > 0 ? dailyResult.currentValue : 0,
                                additionalInvestment: 0, // 日维度不考虑追加投资
                                effectiveInvestDays: 1, // 固定为1天
                                timeRangeAPR: 0, // 日维度的时间范围APR设为0，会使用项目总体APR
                                priceFound: dailyResult.priceFound,
                                missingPriceDate: dailyResult.missingPriceDate,
                                missingPriceToken: dailyResult.missingPriceToken
                            };
                        } else {
                            // 周维度、月维度：使用原有逻辑
                            timeRangeResult = window.DashboardCore.USDTCalculator.calculateTimeRangeEarnings(
                                project, priceMap, timeRange.startDate, timeRange.endDate, unit
                            );
                        }
                    }

                    // 为了保持兼容性，仍然计算项目总体数据
                    // 重要：使用项目总体历史数据，而不是时间范围数据
                    const projectTotalHistoryData = window.DashboardCore.BaseCalculator.calculateProjectHistory(project, priceMap);
                    const projectTotalInvestDays = window.DashboardCore.BaseCalculator.calculateInvestDays(projectTotalHistoryData);
                    const projectTotalUsdtAmounts = window.DashboardCore.USDTCalculator.calculateUSDTAmount(projectTotalHistoryData, unit, priceMap);
                    
                    const projectApr = window.DashboardCore.DerivedCalculator.calculateUSDTBasedAPR(projectTotalUsdtAmounts, projectTotalInvestDays);
                    const earnings = window.DashboardCore.DerivedCalculator.calculateEarnings(projectTotalUsdtAmounts, projectTotalInvestDays);

                    protocolDetails.push({
                        project: project,
                        protocol: project.Protocol || "-",
                        riskLevel: riskLevel,
                        level: project.Level || 0,
                        unit: unit,
                        status: project.Status || "Doing",
                        expectApr: expectApr,
                        // 使用时间范围收益结果中的投资金额（包含追加投资）
                        amountInUsdt: timeRange ? timeRangeResult.totalInvestment : (usdtAmounts.investAmount > 0 ? usdtAmounts.investAmount : 0),
                        // 使用时间范围收益结果
                        usdtTotalEarned: timeRange ? timeRangeResult.timeRangeEarnings : earnings.totalEarned,
                        // 日收益计算：保持各维度一致性 - 都显示平均日收益
                        usdtDailyEarned: timeRange ? 
                            (granularity === 'daily' ? 
                                earnings.dailyEarned : // 日维度：使用项目累计平均日收益
                                (timeRangeResult.effectiveInvestDays > 0 ? timeRangeResult.timeRangeEarnings / timeRangeResult.effectiveInvestDays : 0) // 其他维度：期间平均日收益
                            ) : 
                            earnings.dailyEarned,
                        // 使用时间范围的投资天数
                        investDays: timeRange ? timeRangeResult.effectiveInvestDays : investDays,
                        // APR计算：日维度使用项目总体APR，其他维度使用时间范围APR
                        projectApr: timeRange ? 
                            (granularity === 'daily' ? projectApr : timeRangeResult.timeRangeAPR) : 
                            projectApr,
                        priceFound: timeRange ? timeRangeResult.priceFound : usdtAmounts.priceFound,
                        // 添加缺失价格的详细信息
                        missingPriceDate: timeRange ? timeRangeResult.missingPriceDate : null,
                        missingPriceToken: timeRange ? timeRangeResult.missingPriceToken : null,
                        // 添加追加投资信息
                        additionalInvestment: timeRange ? timeRangeResult.additionalInvestment : 0,
                        // 添加时间范围信息
                        effectiveStart: timeRange ? timeRangeResult.effectiveStart : null,
                        effectiveEnd: timeRange ? timeRangeResult.effectiveEnd : null,
                        // 添加时间组标识符
                        timeGroupId: `time-group-${timeGroup.timeKey}`
                    });
                }

                if (protocolDetails.length === 0) continue;

                // 计算该时间段的汇总数据
                const totalAmountInUsdt = protocolDetails.reduce((sum, p) => sum + p.amountInUsdt, 0);
                const avgAPR = protocolDetails.length > 0 ?
                    protocolDetails.reduce((sum, p) => sum + p.projectApr, 0) / protocolDetails.length : 0;

                // 添加时间汇总行
                const summaryRow = window.DashboardCore.TableBuilder.buildTimeSummaryRow(
                    timeGroup,
                    protocolDetails,
                    totalAmountInUsdt,
                    avgAPR
                );
                // 调试：检查汇总行的列数
                if (summaryRow.length !== 17) {
                    console.warn(`汇总行列数不匹配: 期望17列，实际${summaryRow.length}列`, summaryRow);
                }
                tableData.push(summaryRow);

                // 添加项目详情行
                protocolDetails.sort((a, b) => a.protocol.localeCompare(b.protocol));
                for (const project of protocolDetails) {
                    const projectRow = window.DashboardCore.TableBuilder.buildDateProjectRow(
                        project,
                        totalAmountInUsdt
                    );
                    // 调试：检查项目行的列数
                    if (projectRow.length !== 17) {
                        console.warn(`项目行列数不匹配: 期望17列，实际${projectRow.length}列`, projectRow);
                    }
                    tableData.push(projectRow);
                }
            }

        // 添加总计行到最后
        if (timeGroups.length > 0) {
            const totalRow = window.DashboardCore.TableBuilder.buildDateTotalRow(timeGroups, headers, riskInfo, priceMap, granularity);
            tableData.push(totalRow);
        }

        return {
            headers: headers,
            data: tableData
        };
    }, { headers: [], data: [] }, '构建日期维度表格');
};

/**
 * 构建时间汇总行
 */
window.DashboardCore.TableBuilder.buildTimeSummaryRow = function(timeGroup, protocolDetails, totalAmountInUsdt, avgAPR) {
    return window.DashboardCore.Utils.Error.safeExecute(() => {
        const totalEarned = protocolDetails.reduce((sum, p) => {
            // 修复：应该包含所有收益，包括负数
            if (p.usdtTotalEarned !== -1) {
                return sum + p.usdtTotalEarned;
            }
            return sum;
        }, 0);
        const totalDailyEarned = protocolDetails.reduce((sum, p) => {
            // 修复：日收益也应该包含负数
            if (p.usdtDailyEarned !== -1) {
                return sum + p.usdtDailyEarned;
            }
            return sum;
        }, 0);
        const maxInvestDays = Math.max(...protocolDetails.map(p => p.investDays));

        // 计算期望收益总和
        const totalExpectedEarnings = protocolDetails.reduce((sum, p) => {
            if (p.amountInUsdt > 0 && p.expectApr > 0) {
                const expectedEarnings = window.DashboardCore.DerivedCalculator.calculateExpectedEarnings(
                    p.amountInUsdt, p.expectApr, p.investDays
                );
                return sum + expectedEarnings;
            }
            return sum;
        }, 0);

        // 计算期望收益Gap总和
        const totalExpectedEarningsGap = protocolDetails.reduce((sum, p) => {
            if (p.amountInUsdt > 0 && p.expectApr > 0 && p.usdtTotalEarned !== -1) {
                const expectedEarnings = window.DashboardCore.DerivedCalculator.calculateExpectedEarnings(
                    p.amountInUsdt, p.expectApr, p.investDays
                );
                const expectedGap = window.DashboardCore.DerivedCalculator.calculateExpectedEarningsGap(
                    p.usdtTotalEarned, expectedEarnings
                );
                return sum + expectedGap;
            }
            return sum;
        }, 0);

        // 计算期望APR平均值
        const validExpectAprProjects = protocolDetails.filter(p => p.amountInUsdt > 0 && p.expectApr > 0);
        const avgExpectApr = validExpectAprProjects.length > 0 ?
            validExpectAprProjects.reduce((sum, p) => sum + (p.expectApr * 100), 0) / validExpectAprProjects.length : 0;

        // 计算APR Gap平均值
        const validAprGaps = protocolDetails.filter(p => p.amountInUsdt > 0 && p.expectApr > 0)
            .map(p => window.DashboardCore.DerivedCalculator.calculateAPRGap(p.projectApr, p.expectApr, true));
        const avgAprGap = validAprGaps.length > 0 ?
            validAprGaps.reduce((sum, gap) => sum + gap, 0) / validAprGaps.length : 0;

        // 计算当前APR总比平均值
        const validCurrentAprTotals = protocolDetails.filter(p => p.amountInUsdt > 0)
            .map(p => {
                const projectPercent = totalAmountInUsdt > 0 ? (p.amountInUsdt / totalAmountInUsdt) : 0;
                return window.DashboardCore.DerivedCalculator.calculatePortfolioAPR(projectPercent, p.projectApr);
            });
        const avgCurrentAprTotal = validCurrentAprTotals.length > 0 ?
            validCurrentAprTotals.reduce((sum, apr) => sum + apr, 0) / validCurrentAprTotals.length : 0;

        // 计算期望APR总比平均值
        const validExpectAprTotals = protocolDetails.filter(p => p.amountInUsdt > 0 && p.expectApr > 0)
            .map(p => {
                const projectPercent = totalAmountInUsdt > 0 ? (p.amountInUsdt / totalAmountInUsdt) : 0;
                return window.DashboardCore.DerivedCalculator.calculatePortfolioAPR(projectPercent, p.expectApr * 100);
            });
        const avgExpectAprTotal = validExpectAprTotals.length > 0 ?
            validExpectAprTotals.reduce((sum, apr) => sum + apr, 0) / validExpectAprTotals.length : 0;

        // 计算APR Gap总比平均值
        const avgAprGapTotal = avgCurrentAprTotal - avgExpectAprTotal;

        // 生成时间组唯一标识符
        const timeGroupId = `time-group-${timeGroup.timeKey}`;
        
        // 构建带展开/收起按钮的时间显示文本
        const expandIcon = protocolDetails.length > 0 ? 
            `<span class="expand-toggle" data-target="${timeGroupId}" style="cursor: pointer; margin-right: 8px;">▶️</span>` : '';
        const timeDisplayText = `${expandIcon}<strong>${timeGroup.displayText}</strong>`;

        return [
            timeDisplayText, // 时间（带展开/收起按钮）
            totalAmountInUsdt > 0 ?
                `**${window.DashboardCore.Utils.Number.formatNumber(totalAmountInUsdt)}**` : "**0**", // 投入金额
            totalAmountInUsdt > 0 ? "**100%**" : "**0%**", // 当前占比（汇总行总是100%）
            `**📊 ${protocolDetails.length}个项目**`, // 健康程度显示项目数量
            "", // 项目列为空
            "**-**", // 状态列为空（汇总行）
            totalEarned !== 0 ?
                (totalEarned > 0 ?
                    `<span style="color: #28a745; font-weight: bold;">${window.DashboardCore.Utils.Number.formatNumber(totalEarned)}</span>` :
                    `<span style="color: #dc3545; font-weight: bold;">${window.DashboardCore.Utils.Number.formatNumber(totalEarned)}</span>`) :
                "**0**", // 总收益（带颜色）
            // 期望收益（加粗）
            totalExpectedEarnings > 0 ? `**${window.DashboardCore.Utils.Number.formatNumber(totalExpectedEarnings)}**` : "**0**",
            // 期望收益Gap（带颜色和粗体）
            totalExpectedEarningsGap !== 0 ?
                (totalExpectedEarningsGap > 0 ?
                    `<span style="color: #28a745; font-weight: bold;">${window.DashboardCore.Utils.Number.formatNumber(totalExpectedEarningsGap)}</span>` :
                    `<span style="color: #dc3545; font-weight: bold;">${window.DashboardCore.Utils.Number.formatNumber(totalExpectedEarningsGap)}</span>`) :
                "**0**",
            maxInvestDays > 0 ? `**${maxInvestDays.toFixed(2)}**` : "**0**", // 投资天数（最大值）
            totalDailyEarned !== 0 ?
                (totalDailyEarned > 0 ?
                    `<span style="color: #28a745; font-weight: bold;">${window.DashboardCore.Utils.Number.formatNumber(totalDailyEarned)}</span>` :
                    `<span style="color: #dc3545; font-weight: bold;">${window.DashboardCore.Utils.Number.formatNumber(totalDailyEarned)}</span>`) :
                "**0**", // 日收益（带颜色）
            avgAPR !== 0 ?
                (avgAPR > 0 ?
                    `<span style="color: #28a745; font-weight: bold;">${window.DashboardCore.Utils.Number.formatPercentage(avgAPR)}</span>` :
                    `<span style="color: #dc3545; font-weight: bold;">${window.DashboardCore.Utils.Number.formatPercentage(avgAPR)}</span>`) :
                "**0%**", // 当前APR（带颜色）
            avgExpectApr > 0 ? `**${window.DashboardCore.Utils.Number.formatPercentage(avgExpectApr, 0)}**` : "**0%**", // 期望APR平均值
            validAprGaps.length > 0 ? window.DashboardCore.Utils.Color.getAPRGapColoredText(avgAprGap).replace('<span style="color:', '<span style="color:').replace(';">', '; font-weight: bold;">') : "**0%**", // APR Gap平均值
            avgCurrentAprTotal !== 0 ?
                (avgCurrentAprTotal > 0 ?
                    `<span style="color: #28a745; font-weight: bold;">${window.DashboardCore.Utils.Number.formatPercentage(avgCurrentAprTotal)}</span>` :
                    `<span style="color: #dc3545; font-weight: bold;">${window.DashboardCore.Utils.Number.formatPercentage(avgCurrentAprTotal)}</span>`) :
                "**0%**", // 当前APR总比平均值（带颜色）
            avgExpectAprTotal > 0 ? `**${window.DashboardCore.Utils.Number.formatPercentage(avgExpectAprTotal)}**` : "**0%**", // 期望APR总比平均值
            validAprGaps.length > 0 ? window.DashboardCore.Utils.Color.getAPRGapColoredText(avgAprGapTotal).replace('<span style="color:', '<span style="color:').replace(';">', '; font-weight: bold;">') : "**0%**" // APR Gap总比平均值
        ];
    }, [], '构建时间汇总行');
};

/**
 * 构建日期项目详情行
 */
window.DashboardCore.TableBuilder.buildDateProjectRow = function(project, totalAmountInUsdt) {
    return window.DashboardCore.Utils.Error.safeExecute(() => {
        const statusColor = project.status === "Doing" ? "#28a745" : "#6c757d";
        const statusIcon = project.status === "Doing" ? "🟢" : "⚪";

        // 计算APR相关指标
        const projectPercent = totalAmountInUsdt > 0 ? (project.amountInUsdt / totalAmountInUsdt) : 0;
        const projectExpectApr = project.expectApr * 100;
        const projectAprGap = window.DashboardCore.DerivedCalculator.calculateAPRGap(project.projectApr, project.expectApr, project.amountInUsdt > 0);
        const projectPortfolioCurrentApr = window.DashboardCore.DerivedCalculator.calculatePortfolioAPR(projectPercent, project.projectApr);
        const projectPortfolioExpectApr = window.DashboardCore.DerivedCalculator.calculatePortfolioAPR(projectPercent, projectExpectApr);
        const projectPortfolioAprGap = projectPortfolioCurrentApr - projectPortfolioExpectApr;

        // 计算项目的期望收益和期望收益Gap
        const expectedEarnings = window.DashboardCore.DerivedCalculator.calculateExpectedEarnings(
            project.amountInUsdt, project.expectApr, project.investDays
        );
        const expectedEarningsGap = window.DashboardCore.DerivedCalculator.calculateExpectedEarningsGap(
            project.usdtTotalEarned, expectedEarnings
        );

        // 生成项目行，包含时间组标识符和行类型标识
        const projectRow = [
            "", // 时间列为空（项目行不显示时间）
            project.amountInUsdt > 0 ?
                window.DashboardCore.Utils.Number.formatNumber(project.amountInUsdt) : "0", // 投入金额
            window.DashboardCore.Utils.Number.formatPercentage(projectPercent * 100), // 当前占比
            "-", // 健康程度（项目行显示横杠）
            project.project.file.link, // 项目
            `<span style="color: ${statusColor};">${statusIcon} ${project.status}</span>`, // 状态列：使用带颜色标记的状态显示
            project.usdtTotalEarned === -1 ? 
                (project.missingPriceDate && project.missingPriceToken ? 
                    `${project.missingPriceToken} ${project.missingPriceDate} 价格数据未找到` : 
                    "价格数据未找到") :
                (project.usdtTotalEarned !== 0 ? window.DashboardCore.Utils.Color.formatColoredNumber(project.usdtTotalEarned) : "0"), // 总收益（带颜色）
            // 期望收益
            expectedEarnings > 0 ? window.DashboardCore.Utils.Number.formatNumber(expectedEarnings) : "0",
            // 期望收益Gap（带颜色）
            project.usdtTotalEarned === -1 ? 
                (project.missingPriceDate && project.missingPriceToken ? 
                    `${project.missingPriceToken} ${project.missingPriceDate} 价格数据未找到` : 
                    "价格数据未找到") :
                (expectedEarningsGap !== 0 ? window.DashboardCore.Utils.Color.formatColoredNumber(expectedEarningsGap) : "0"),
            project.investDays > 0 ? `${project.investDays.toFixed(2)}` : "0", // 投资天数
            project.usdtDailyEarned === -1 ? 
                (project.missingPriceDate && project.missingPriceToken ? 
                    `${project.missingPriceToken} ${project.missingPriceDate} 价格数据未找到` : 
                    "价格数据未找到") :
                (project.usdtDailyEarned !== 0 ? window.DashboardCore.Utils.Color.formatColoredNumber(project.usdtDailyEarned) : "0"), // 日收益（带颜色）
            window.DashboardCore.Utils.Color.formatColoredPercentage(project.projectApr), // 当前APR（带颜色）
            project.amountInUsdt > 0 && projectExpectApr > 0 ? window.DashboardCore.Utils.Number.formatPercentage(projectExpectApr, 0) : "0%", // 期望APR
            project.amountInUsdt > 0 && projectExpectApr > 0 ? window.DashboardCore.Utils.Color.getAPRGapColoredText(projectAprGap) : "0%", // APR Gap
            window.DashboardCore.Utils.Color.formatColoredPercentage(projectPortfolioCurrentApr), // 当前APR总比（带颜色）
            window.DashboardCore.Utils.Number.formatPercentage(projectPortfolioExpectApr), // 期望APR总比
            project.amountInUsdt > 0 && projectExpectApr > 0 ? window.DashboardCore.Utils.Color.getAPRGapColoredText(projectPortfolioAprGap) : "0%" // APR Gap总比
        ];

        // 为项目行添加时间组标识符和行类型标识
        projectRow.timeGroupId = project.timeGroupId;
        projectRow.rowType = 'detail';
        
        return projectRow;
    }, [], '构建日期项目详情行');
};

/**
 * 构建日期视图总计行
 * @param {Array} timeGroups - 时间组数据
 * @param {Array} headers - 表头数组
 * @param {Object} riskInfo - 风险信息
 * @param {Object} priceMap - 价格映射
 * @param {string} granularity - 时间粒度
 * @returns {Array} 表格行数据
 */
window.DashboardCore.TableBuilder.buildDateTotalRow = function(timeGroups, headers, riskInfo, priceMap, granularity = 'monthly') {
    return window.DashboardCore.Utils.Error.safeExecute(() => {
        // 收集所有项目数据
        let allProjects = [];
        let totalAmountInUsdt = 0;
        let totalTotalEarned = 0;
        let totalExpectedEarnings = 0;
        let totalExpectedEarningsGap = 0;
        let totalDailyEarned = 0;
        let maxInvestDays = 0;
        let totalProjectCount = 0;

        // 遍历所有时间组，收集项目数据
        for (const timeGroup of timeGroups) {
            for (const item of timeGroup.projectData) {
                const project = item.project;

                // 避免重复计算同一个项目
                const projectKey = project.file.name;
                if (!allProjects.find(p => p.file.name === projectKey)) {
                    allProjects.push(project);
                    totalProjectCount++;

                    // 这里需要重新计算项目数据，因为我们需要全局的数据而不是时间段的数据
                    // 使用项目的完整历史数据
                    const historyData = window.DashboardCore.BaseCalculator.calculateProjectHistory(project, priceMap || {});
                    if (historyData) {
                        const unit = (project.Unit || 'USDT').toUpperCase();
                        const riskLevel = project.Risk || 0;
                        const expectApr = (riskInfo[riskLevel] && riskInfo[riskLevel].expectApr) ? riskInfo[riskLevel].expectApr : 0;
                        const investDays = window.DashboardCore.BaseCalculator.calculateInvestDays(historyData);
                        const usdtAmounts = window.DashboardCore.USDTCalculator.calculateUSDTAmount(historyData, unit, priceMap || {});
                        const earnings = window.DashboardCore.DerivedCalculator.calculateEarnings(usdtAmounts, investDays);

                        totalAmountInUsdt += usdtAmounts.investAmount > 0 ? usdtAmounts.investAmount : 0;
                        
                        // 只有当earnings不是-1时才累加
                        if (earnings.totalEarned !== -1) {
                            totalTotalEarned += earnings.totalEarned;
                        }
                        if (earnings.dailyEarned !== -1) {
                            totalDailyEarned += earnings.dailyEarned;
                        }
                        maxInvestDays = Math.max(maxInvestDays, investDays);

                        // 计算期望收益
                        if (usdtAmounts.investAmount > 0 && expectApr > 0) {
                            const expectedEarnings = window.DashboardCore.DerivedCalculator.calculateExpectedEarnings(
                                usdtAmounts.investAmount, expectApr, investDays
                            );
                            totalExpectedEarnings += expectedEarnings;

                            // 计算期望收益Gap
                            if (earnings.totalEarned !== -1) {
                                const expectedGap = window.DashboardCore.DerivedCalculator.calculateExpectedEarningsGap(
                                    earnings.totalEarned, expectedEarnings
                                );
                                totalExpectedEarningsGap += expectedGap;
                            }
                        }
                    }
                }
            }
        }

        // 计算平均APR
        const totalCurrentApr = allProjects.length > 0 ?
            allProjects.reduce((sum, project) => {
                const historyData = window.DashboardCore.BaseCalculator.calculateProjectHistory(project, priceMap || {});
                if (historyData) {
                    const unit = (project.Unit || 'USDT').toUpperCase();
                    const investDays = window.DashboardCore.BaseCalculator.calculateInvestDays(historyData);
                    const usdtAmounts = window.DashboardCore.USDTCalculator.calculateUSDTAmount(historyData, unit, priceMap || {});
                    const projectApr = window.DashboardCore.DerivedCalculator.calculateUSDTBasedAPR(usdtAmounts, investDays);
                    return sum + projectApr;
                }
                return sum;
            }, 0) / allProjects.length : 0;

        // 计算期望APR（基于风险等级）
        const totalExpectApr = allProjects.length > 0 ?
            allProjects.reduce((sum, project) => {
                const riskLevel = project.Risk || 0;
                const expectApr = (riskInfo[riskLevel] && riskInfo[riskLevel].expectApr) ? riskInfo[riskLevel].expectApr * 100 : 0;
                return sum + expectApr;
            }, 0) / allProjects.length : 0;

        const totalAprGap = totalCurrentApr - totalExpectApr;

        // 组合APR总比就是当前APR和期望APR（因为总占比是100%）
        const totalPortfolioCurrentApr = totalCurrentApr;
        const totalPortfolioExpectApr = totalExpectApr;
        const totalPortfolioAprGap = totalAprGap;

        // 构建总计行，确保列数与表头匹配
        return [
            `**总计**`, // 第一列：时间
            `**${window.DashboardCore.Utils.Number.formatNumber(totalAmountInUsdt)}**`, // 投入金额（加粗）
            `**100%**`, // 当前占比（加粗）
            `**📊 ${totalProjectCount}个项目**`, // 健康程度显示项目总数（加粗）
            "", // 项目列为空
            "**-**", // 状态列为空（总计行）
            // 总收益（带颜色和粗体）
            totalTotalEarned !== 0 ?
                (totalTotalEarned > 0 ?
                    `<span style="color: #28a745; font-weight: bold;">${window.DashboardCore.Utils.Number.formatNumber(totalTotalEarned)}</span>` :
                    `<span style="color: #dc3545; font-weight: bold;">${window.DashboardCore.Utils.Number.formatNumber(totalTotalEarned)}</span>`) :
                "**0**",
            // 期望收益（加粗）
            totalExpectedEarnings > 0 ? `**${window.DashboardCore.Utils.Number.formatNumber(totalExpectedEarnings)}**` : "**0**",
            // 期望收益Gap（带颜色和粗体）
            totalExpectedEarningsGap !== 0 ?
                (totalExpectedEarningsGap > 0 ?
                    `<span style="color: #28a745; font-weight: bold;">${window.DashboardCore.Utils.Number.formatNumber(totalExpectedEarningsGap)}</span>` :
                    `<span style="color: #dc3545; font-weight: bold;">${window.DashboardCore.Utils.Number.formatNumber(totalExpectedEarningsGap)}</span>`) :
                "**0**",
            maxInvestDays > 0 ? `**${maxInvestDays.toFixed(2)}**` : "**0**", // 投资天数（加粗）
            // 日收益（带颜色和粗体）
            totalDailyEarned !== 0 ?
                (totalDailyEarned > 0 ?
                    `<span style="color: #28a745; font-weight: bold;">${window.DashboardCore.Utils.Number.formatNumber(totalDailyEarned)}</span>` :
                    `<span style="color: #dc3545; font-weight: bold;">${window.DashboardCore.Utils.Number.formatNumber(totalDailyEarned)}</span>`) :
                "**0**",
            // 当前APR（带颜色和粗体）
            totalCurrentApr !== 0 ?
                (totalCurrentApr > 0 ?
                    `<span style="color: #28a745; font-weight: bold;">${window.DashboardCore.Utils.Number.formatPercentage(totalCurrentApr)}</span>` :
                    `<span style="color: #dc3545; font-weight: bold;">${window.DashboardCore.Utils.Number.formatPercentage(totalCurrentApr)}</span>`) :
                `**${window.DashboardCore.Utils.Number.formatPercentage(totalCurrentApr)}**`,
            `**${window.DashboardCore.Utils.Number.formatPercentage(totalExpectApr, 0)}**`, // 期望APR（加粗）
            window.DashboardCore.Utils.Color.getAPRGapColoredText(totalAprGap).replace('<span style="color:', '<span style="color:').replace(';">', '; font-weight: bold;">'), // APR Gap（加粗）
            // 当前APR总比（带颜色和粗体）
            totalPortfolioCurrentApr !== 0 ?
                (totalPortfolioCurrentApr > 0 ?
                    `<span style="color: #28a745; font-weight: bold;">${window.DashboardCore.Utils.Number.formatPercentage(totalPortfolioCurrentApr)}</span>` :
                    `<span style="color: #dc3545; font-weight: bold;">${window.DashboardCore.Utils.Number.formatPercentage(totalPortfolioCurrentApr)}</span>`) :
                `**${window.DashboardCore.Utils.Number.formatPercentage(totalPortfolioCurrentApr)}**`,
            `**${window.DashboardCore.Utils.Number.formatPercentage(totalPortfolioExpectApr)}**`, // 期望APR总比（加粗）
            window.DashboardCore.Utils.Color.getAPRGapColoredText(totalPortfolioAprGap).replace('<span style="color:', '<span style="color:').replace(';">', '; font-weight: bold;">') // APR Gap总比（加粗）
        ];
    }, [], '构建日期视图总计行');
};

console.log('Dashboard TableBuilder (Complete) module loaded');
