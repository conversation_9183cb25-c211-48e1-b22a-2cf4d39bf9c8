/**
 * Dashboard 渲染模块
 * 
 * 负责将数据渲染为HTML：
 * - 表格渲染
 * - 单元格内容处理
 * - 样式应用
 * 
 * @module DashboardRenderer
 */

// 确保核心对象存在
window.DashboardCore = window.DashboardCore || {};

/**
 * 渲染器
 */
window.DashboardCore.Renderer = {
    /**
     * 处理单元格内容，清理Markdown格式并处理链接
     * @param {*} cell - 单元格内容
     * @param {number} columnIndex - 列索引
     * @param {Array} headers - 表头数组
     * @returns {string} 处理后的HTML内容
     */
    processCellContent(cell, columnIndex, headers) {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            if (!cell || cell === '') return '';

            // 检查是否是Dataview链接对象
            if (cell && typeof cell === 'object' && cell.path) {
                // 这是一个Dataview链接对象
                const fileName = cell.path.split('/').pop().replace('.md', '') || cell.path;
                return `<a href="obsidian://open?file=${encodeURIComponent(cell.path)}" style="color: var(--link-color); text-decoration: underline;">${fileName}</a>`;
            }

            let content = cell.toString();

            // 如果内容已经包含HTML标签，直接返回（避免重复处理）
            if (content.includes('<span') || content.includes('<strong') || content.includes('<a')) {
                return content;
            }

            // 清理Markdown格式的粗体标记，转换为HTML
            content = content.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

            // 处理项目列的链接（通常是第5列，索引为4）
            const projectColumnIndex = headers.findIndex(h => h.includes('项目'));
            if (columnIndex === projectColumnIndex && content.includes('[[') && content.includes(']]')) {
                // 提取链接路径和显示文本
                const linkMatch = content.match(/\[\[(.*?)\]\]/);
                if (linkMatch) {
                    const linkPath = linkMatch[1];
                    const fileName = linkPath.split('/').pop().replace('.md', '') || linkPath;
                    // 创建Obsidian内部链接
                    content = `<a href="obsidian://open?file=${encodeURIComponent(linkPath)}" style="color: var(--link-color); text-decoration: underline;">${fileName}</a>`;
                }
            }

            // 处理其他可能的链接格式
            if (content.includes('[[') && content.includes(']]') && !content.includes('<a')) {
                content = content.replace(/\[\[(.*?)\]\]/g, (match, linkPath) => {
                    const fileName = linkPath.split('/').pop().replace('.md', '') || linkPath;
                    return `<a href="obsidian://open?file=${encodeURIComponent(linkPath)}" style="color: var(--link-color); text-decoration: underline;">${fileName}</a>`;
                });
            }

            return content;
        }, '', '处理单元格内容');
    },

    /**
     * 渲染带固定表头和第一列的表格
     * @param {Array} headers - 表头数组
     * @param {Array} data - 表格数据
     * @param {string} containerId - 容器ID
     * @returns {string} HTML字符串
     */
    renderFixedTable(headers, data, containerId = 'dashboard-table') {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            const maxHeight = window.DashboardCore.getConfig('TABLE_MAX_HEIGHT', '80vh');
            const fontSize = window.DashboardCore.getConfig('TABLE_FONT_SIZE', '14px');
            const borderRadius = window.DashboardCore.getConfig('TABLE_BORDER_RADIUS', '6px');

            // 添加CSS样式用于折叠/展开功能
            const collapseStyles = `
                <style>
                    .expand-toggle {
                        cursor: pointer;
                        margin-right: 8px;
                        font-size: 12px;
                        transition: transform 0.2s ease;
                        display: inline-block;
                    }
                    .expand-toggle:hover {
                        transform: scale(1.1);
                    }
                    .detail-row {
                        display: none;
                        transition: all 0.3s ease;
                    }
                    .detail-row.expanded {
                        display: table-row;
                    }
                    .time-group-summary {
                        background-color: var(--background-secondary);
                        font-weight: bold;
                    }
                    .time-group-summary:hover {
                        background-color: var(--background-modifier-hover) !important;
                    }
                    .time-group-summary td:first-child {
                        background-color: var(--background-secondary) !important;
                    }
                </style>
            `;

            let tableHTML = `
                ${collapseStyles}
                <div id="${containerId}" class="dashboard-table-container" style="position: relative; overflow: auto; max-height: ${maxHeight}; border: 1px solid var(--background-modifier-border); border-radius: ${borderRadius};">
                    <table class="dashboard-fixed-table" style="width: 100%; border-collapse: collapse; font-size: ${fontSize};">
                        <thead style="position: sticky; top: 0; z-index: 10; background-color: var(--background-primary);">
                            <tr>`;

            // 渲染表头
            headers.forEach((header, index) => {
                const isFirstColumn = index === 0;
                const stickyStyle = isFirstColumn ?
                    'position: sticky; left: 0; z-index: 15; background-color: var(--background-primary); border-right: 2px solid var(--background-modifier-border); min-width: 120px;' : '';
                tableHTML += `<th style="background-color: var(--background-primary); border: 1px solid var(--background-modifier-border); padding: 8px 12px; font-weight: bold; white-space: nowrap; ${stickyStyle}">${header}</th>`;
            });

            tableHTML += `</tr></thead><tbody>`;

            // 渲染数据行
            data.forEach((row, rowIndex) => {
                // 判断是否为时间汇总行（第一列包含展开按钮）
                const isTimeSummaryRow = Array.isArray(row) && row.length > 0 && 
                                       row[0] && row[0].includes && row[0].includes('expand-toggle');
                
                // 判断是否为项目详情行
                const isDetailRow = row.rowType === 'detail';
                const timeGroupId = row.timeGroupId || '';
                
                // 构建行的CSS类和属性
                let rowClasses = [];
                let rowAttributes = '';
                
                if (isTimeSummaryRow) {
                    rowClasses.push('time-group-summary');
                    rowAttributes = `data-row-type="summary"`;
                } else if (isDetailRow) {
                    rowClasses.push('detail-row');
                    rowAttributes = `data-row-type="detail" data-time-group="${timeGroupId}"`;
                } else {
                    rowAttributes = `data-row-type="total"`;
                }

                const rowClassAttr = rowClasses.length > 0 ? ` class="${rowClasses.join(' ')}"` : '';
                const hoverStyle = isTimeSummaryRow ? 
                    '' : `onmouseover="this.style.backgroundColor='var(--background-secondary)'; this.querySelectorAll('td:first-child').forEach(td => td.style.backgroundColor='var(--background-secondary)');" onmouseout="this.style.backgroundColor=''; this.querySelectorAll('td:first-child').forEach(td => td.style.backgroundColor='var(--background-primary)');"`;

                tableHTML += `<tr${rowClassAttr} ${rowAttributes} style="border-bottom: 1px solid var(--background-modifier-border);" ${hoverStyle}>`;
                
                row.forEach((cell, cellIndex) => {
                    const isFirstColumn = cellIndex === 0;
                    let stickyStyle = isFirstColumn ?
                        'position: sticky; left: 0; z-index: 5; background-color: var(--background-primary); border-right: 2px solid var(--background-modifier-border); font-weight: bold; min-width: 120px;' : '';
                    
                    // 为时间汇总行的第一列添加特殊样式
                    if (isTimeSummaryRow && isFirstColumn) {
                        stickyStyle = stickyStyle.replace('background-color: var(--background-primary)', 'background-color: var(--background-secondary)');
                    }

                    // 处理单元格内容
                    const processedContent = this.processCellContent(cell, cellIndex, headers);

                    tableHTML += `<td style="border: 1px solid var(--background-modifier-border); padding: 6px 10px; white-space: nowrap; ${stickyStyle}">${processedContent}</td>`;
                });
                
                tableHTML += `</tr>`;
            });

            tableHTML += `</tbody></table></div>`;

            return tableHTML;
        }, '<div>渲染表格时出错</div>', '渲染固定表格');
    },

    /**
     * 渲染简单表格（无固定列）
     * @param {Array} headers - 表头数组
     * @param {Array} data - 表格数据
     * @param {string} containerId - 容器ID
     * @returns {string} HTML字符串
     */
    renderSimpleTable(headers, data, containerId = 'simple-table') {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            const fontSize = window.DashboardCore.getConfig('TABLE_FONT_SIZE', '14px');

            let tableHTML = `
                <div id="${containerId}" class="dashboard-simple-table">
                    <table style="width: 100%; border-collapse: collapse; font-size: ${fontSize};">
                        <thead>
                            <tr>`;

            // 渲染表头
            headers.forEach(header => {
                tableHTML += `<th style="border: 1px solid var(--background-modifier-border); padding: 8px 12px; font-weight: bold; background-color: var(--background-secondary);">${header}</th>`;
            });

            tableHTML += `</tr></thead><tbody>`;

            // 渲染数据行
            data.forEach(row => {
                tableHTML += `<tr>`;
                row.forEach((cell, cellIndex) => {
                    const processedContent = this.processCellContent(cell, cellIndex, headers);
                    tableHTML += `<td style="border: 1px solid var(--background-modifier-border); padding: 6px 10px;">${processedContent}</td>`;
                });
                tableHTML += `</tr>`;
            });

            tableHTML += `</tbody></table></div>`;

            return tableHTML;
        }, '<div>渲染简单表格时出错</div>', '渲染简单表格');
    },

    /**
     * 渲染加载指示器
     * @param {string} message - 加载消息
     * @returns {string} HTML字符串
     */
    renderLoadingIndicator(message = '正在加载数据...') {
        return `
            <div class="dashboard-loading" style="
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 40px;
                color: var(--text-muted);
                font-style: italic;
            ">
                <div style="margin-right: 10px;">⏳</div>
                <div>${message}</div>
            </div>
        `;
    },

    /**
     * 渲染错误信息
     * @param {string} error - 错误信息
     * @param {string} details - 详细信息
     * @returns {string} HTML字符串
     */
    renderError(error, details = '') {
        return `
            <div class="dashboard-error" style="
                padding: 20px;
                border: 1px solid #dc3545;
                border-radius: 6px;
                background-color: #f8d7da;
                color: #721c24;
                margin: 10px 0;
            ">
                <div style="font-weight: bold; margin-bottom: 10px;">❌ 错误</div>
                <div style="margin-bottom: 10px;">${error}</div>
                ${details ? `<div style="font-size: 12px; color: #6c757d;">${details}</div>` : ''}
            </div>
        `;
    },

    /**
     * 渲染空数据提示
     * @param {string} message - 提示消息
     * @returns {string} HTML字符串
     */
    renderEmptyState(message = '暂无数据') {
        return `
            <div class="dashboard-empty" style="
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 60px 20px;
                color: var(--text-muted);
                font-style: italic;
                border: 1px dashed var(--background-modifier-border);
                border-radius: 6px;
                background-color: var(--background-secondary);
            ">
                <div style="text-align: center;">
                    <div style="font-size: 48px; margin-bottom: 10px;">📊</div>
                    <div>${message}</div>
                </div>
            </div>
        `;
    },

    /**
     * 渲染统计卡片
     * @param {string} title - 标题
     * @param {string} value - 值
     * @param {string} subtitle - 副标题
     * @param {string} color - 颜色
     * @returns {string} HTML字符串
     */
    renderStatCard(title, value, subtitle = '', color = 'var(--text-normal)') {
        return `
            <div class="dashboard-stat-card" style="
                padding: 16px;
                border: 1px solid var(--background-modifier-border);
                border-radius: 6px;
                background-color: var(--background-primary);
                text-align: center;
                min-width: 120px;
            ">
                <div style="font-size: 12px; color: var(--text-muted); margin-bottom: 4px;">${title}</div>
                <div style="font-size: 20px; font-weight: bold; color: ${color}; margin-bottom: 4px;">${value}</div>
                ${subtitle ? `<div style="font-size: 10px; color: var(--text-muted);">${subtitle}</div>` : ''}
            </div>
        `;
    }
};

// 向后兼容的全局函数
window.DashboardTableRenderer = window.DashboardCore.Renderer;

console.log('Dashboard Renderer module loaded');
