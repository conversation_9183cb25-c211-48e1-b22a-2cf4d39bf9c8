# Web3 Farm Dashboard 开发者文档

## 📋 目录

- [系统概述](#-系统概述)
- [架构设计](#-架构设计)
- [核心功能](#-核心功能)
- [模块详解](#-模块详解)
- [数据流程](#-数据流程)
- [缓存机制](#-缓存机制)
- [开发指南](#-开发指南)
- [故障排除](#-故障排除)
- [版本历史](#-版本历史)

## 🎯 系统概述

Web3 Farm Dashboard 是一个专为 DeFi (Decentralized Finance) 投资追踪设计的 Obsidian 仪表板系统。它提供实时的投资组合分析、风险管理和收益计算功能。

### 核心价值

- **风险维度分析**: 按风险等级分组显示投资项目
- **时间维度分析**: 按日/周/月/年维度进行时间段分析
- **实时计算**: APR、收益、投资天数等指标的实时计算
- **智能缓存**: 性能优化的多层缓存系统
- **模块化架构**: 8个独立的JavaScript模块

## 🏗️ 架构设计

### 模块化设计

系统采用模块化架构，包含8个核心模块：

```
web3/farm/
├── dashboard.md                 # 🚪 主入口文件
├── lib/                        # 📚 核心库目录
│   ├── dashboard-config.js      # ⚙️ 配置管理
│   ├── dashboard-utils.js       # 🛠️ 工具函数
│   ├── dashboard-data-loader.js # 📥 数据加载
│   ├── dashboard-calculator.js  # 🧮 计算引擎
│   ├── dashboard-table-builder.js # 📊 表格构建
│   ├── dashboard-renderer.js    # 🎨 渲染引擎
│   ├── dashboard-ui.js          # 🖱️ 用户界面
│   └── dashboard-core.js        # 🔧 核心API
├── data/                       # 📁 配置数据
└── history/                    # 📈 投资历史
```

### 依赖关系

```mermaid
graph TD
    A[dashboard-config.js] --> B[dashboard-utils.js]
    B --> C[dashboard-data-loader.js]
    C --> D[dashboard-calculator.js]
    D --> E[dashboard-table-builder.js]
    E --> F[dashboard-renderer.js]
    F --> G[dashboard-ui.js]
    G --> H[dashboard-core.js]
    H --> I[dashboard.md]
```

## ⚡ 核心功能

### 1. 双维度分析

#### 风险维度视图
- 按风险等级（0-4级）分组显示项目
- 风险占比和健康状态监控
- 仓位建议和操作提醒
- **Done/Doing状态切换** (v4.2.0新增): 支持查看当前投资和历史投资的风险分布

#### 日期维度视图（v3.2.0新特性）
- **时间段分析工具**: 真正的时间段分析，而非项目总体数据展示
- **统一时间范围逻辑**: 使用max/min逻辑确定有效时间范围
- **历史数据插值**: 只使用历史数据，避免未来信息泄露
- **追加投资处理**: 追加投资计入总投资金额，而非从收益中扣除
- **折叠/展开功能** (v3.3.0新增): 项目详情默认折叠，支持按时间段展开/收起

##### 日期维度列设计 (v4.3.0)
**"总收益"列**（按维度不同含义）:
- **日维度**: 当日 vs 前一日的价值变化（日间增量）
- **周维度**: 本周期内的总收益（周初到周末）
- **月维度**: 本月期内的总收益（月初到月末）

**"日收益"列**（各维度保持一致）:
- **日维度**: 项目累计平均日收益（总收益 ÷ 总投资天数）
- **周维度**: 本周期平均日收益（周收益 ÷ 周天数）
- **月维度**: 本月期平均日收益（月收益 ÷ 月天数）

**设计理念**:
- 总收益列在日维度提供独特的"日间变化"视角
- 日收益列在所有维度保持"平均日收益"的统一含义
- 既满足日间趋势分析需求，又保持维度间的逻辑一致性

### 2. 智能计算引擎

#### 统一价值公式 (v4.0.0)
所有项目类型使用统一的收益计算公式：
```javascript
收益 = (最终价格 × 最终数量) - (初始价格 × 初始数量) - 追加投资价值
```

**适用场景**:
- **Trade项目**: 纯价格收益计算 (数量不变)
- **DeFi项目**: 数量收益 + 价格收益
- **合约项目**: 头寸价值变化收益
- **稳定币项目**: 纯数量收益 (价格恒为1)

#### 核心计算逻辑
- **时间范围收益**: `calculateTimeRangeEarnings()` - v3.2.0重写
- **有效时间范围**: `effectiveStart = max(projectStart, periodStart)`
- **有效时间范围**: `effectiveEnd = min(projectEnd, periodEnd)`
- **APR计算**: 基于USDT价值的精确APR计算
- **期望收益**: 基于风险等级的期望收益计算

#### 数据插值策略
```javascript
// 只使用历史数据的插值逻辑
getBalanceAtDateBefore(balanceEntries, targetDate) {
    // 只考虑目标日期之前（含当天）的记录
    const beforeEntries = balanceEntries.filter(entry => 
        entryDate <= targetDate
    );
    return latestBefore.balance;
}
```

### 3. 多层缓存系统

#### 缓存层级
1. **数据层缓存**: 5分钟TTL，原始数据缓存
2. **表格层缓存**: 全局变量缓存，按视图和粒度分类
3. **渲染缓存**: 避免重复的DOM操作

#### 缓存管理
```javascript
// 强制清除所有缓存
window.DashboardCore.DataLoader.Cache.clear();
delete window.DashboardTableData;
delete window.DashboardDateBaseData; 
delete window.DashboardDateTableData;
```

## 📦 模块详解

### 1. dashboard-config.js
配置管理模块，包含系统级配置、颜色主题、阈值设置等。

**主要功能:**
- 默认配置定义
- 配置获取和设置
- 颜色和主题管理

### 2. dashboard-utils.js
工具函数模块，提供通用的工具函数。

**主要功能:**
- 日期处理函数
- 数字格式化
- 数据验证
- 错误处理

### 3. dashboard-data-loader.js ⚠️
数据加载模块，负责从各种数据源加载数据。

**⚠️ 价格数据格式注意事项:**

系统现已简化，仅支持单一的frontmatter格式：

#### 📊 唯一支持的价格数据格式
**Frontmatter YAML格式** (当前唯一格式)
```yaml
---
provider: Binance & CoinGecko (Historical)
format: frontmatter
prices:
  BTC:
    2025-05-10: 104809.53
    2025-05-13: 104103.72
---
```

#### ✅ 简化后的优势
- **单一格式**: 消除了格式检测和兼容性问题
- **原生支持**: 利用DataviewJS原生frontmatter解析
- **代码简洁**: 移除了所有冗余的解析逻辑
- **维护简单**: 不再需要处理多格式并存问题

#### 🔧 价格数据更新流程

**操作步骤:**
1. 运行价格更新脚本: `python update_prices.py`
2. 系统自动生成frontmatter格式文件
3. 在dashboard中点击"🔄 强制刷新"
4. 验证价格数据正确加载

**故障排除:**
```javascript
// 检查价格数据加载状态
console.log('价格数据:', window.DashboardCore.dv.page('web3/farm/data/token_prices').prices);

// 强制清除价格相关缓存
window.DashboardCore.DataLoader.Cache.clear();
```

**主要功能:**
- 配置文件解析
- 项目数据加载
- **价格数据处理** (支持多种格式，推荐frontmatter)
- 缓存管理

### 4. dashboard-calculator.js ⭐ 
计算引擎模块，包含所有核心业务计算逻辑。

**v3.2.0 重大更新:**
- 重写 `calculateTimeRangeEarnings()` 函数
- 新增 `getBalanceAtDateBefore()` 历史数据插值
- 新增 `calculateTimeRangeAPR()` 时间范围APR计算
- 实现统一时间范围逻辑

**核心函数:**
```javascript
// 时间范围收益计算（v3.2.0）
calculateTimeRangeEarnings(project, priceMap, startDate, endDate, unit)

// 历史数据插值（v3.2.0新增）  
getBalanceAtDateBefore(balanceEntries, targetDate)

// 时间范围APR计算（v3.2.0新增）
calculateTimeRangeAPR(earnings, investment, days)
```

### 5. dashboard-table-builder.js ⭐
表格构建模块，负责构建各种维度的表格数据。

**v3.2.0 重要修复:**
- 修复负数收益聚合bug
- 统一使用时间范围计算结果
- 完善汇总行计算逻辑

**v3.3.0 折叠/展开功能**:
- 时间汇总行添加展开/收起按钮
- 项目详情行添加时间组标识
- 支持行类型分类和状态管理

**核心函数:**
```javascript
// 风险维度表格构建
buildRiskTable(data)

// 日期维度表格构建  
buildDateTable(data, granularity)

// 时间汇总行构建（v3.2.0修复，v3.3.0增强）
buildTimeSummaryRow(timeGroup, protocolDetails, totalAmountInUsdt, avgAPR)

// 项目详情行构建（v3.3.0增强折叠功能）
buildDateProjectRow(project, totalAmountInUsdt)
```

### 6. dashboard-renderer.js ⭐
渲染引擎模块，负责HTML渲染和DOM操作。

**v3.3.0 折叠/展开UI增强**:
- 支持可折叠表格行的CSS样式
- 时间汇总行特殊背景样式
- 展开/收起按钮悬停效果
- 平滑动画过渡效果

**主要功能:**
- 固定表头表格渲染
- 统计卡片渲染
- 响应式设计支持
- 可折叠表格样式

### 7. dashboard-ui.js ⭐
用户界面模块，负责UI组件和交互逻辑。

**v3.3.0 新增折叠/展开功能**:
- 日期视图项目详情折叠/展开控制
- 全局展开/收起操作
- 状态持久化到本地存储
- 响应式布局优化

**主要功能:**
- 视图切换器
- 控制按钮
- 事件处理
- 折叠/展开状态管理

### 8. dashboard-core.js ⭐
核心API模块，提供统一的接口和系统管理。

**v3.2.0 缓存增强:**
- 增强的 `refreshView()` 方法
- 完整的缓存清除功能
- 详细的操作日志

**核心API:**
```javascript
// 系统初始化
await DashboardCore.API.initialize(dv, config)

// 强制刷新（清除所有缓存）
await DashboardCore.API.refreshView()

// 视图切换
await DashboardCore.API.switchMainView('risk'|'date')
await DashboardCore.API.switchDateGranularity('daily'|'weekly'|'monthly'|'yearly')

// 风险状态切换 (v4.2.0新增)
await DashboardCore.API.switchRiskProjectStatus('Doing'|'Done')
```

## 🔄 数据流程

### 数据加载流程
```mermaid
graph LR
    A[配置文件] --> B[数据加载器]
    C[项目历史] --> B
    D[价格数据] --> B
    B --> E[计算引擎]
    E --> F[表格构建]
    F --> G[渲染引擎]
    G --> H[用户界面]
```

### 时间维度计算流程（v3.2.0）
```mermaid
graph TD
    A[项目数据] --> B[提取时间范围]
    B --> C[计算有效时间范围]
    C --> D[历史数据插值]
    D --> E[收益计算]
    E --> F[APR计算]
    F --> G[表格聚合]
    G --> H[最终渲染]
```

## 💾 缓存机制

### 缓存策略

#### 1. 数据层缓存
- **位置**: `DashboardCore.DataLoader.Cache`
- **TTL**: 5分钟 (可配置)
- **内容**: 原始数据和解析结果

#### 2. 表格层缓存
- **风险维度**: `window.DashboardTableData`
- **日期基础数据**: `window.DashboardDateBaseData`
- **日期表格数据**: `window.DashboardDateTableData[granularity]`

#### 3. 缓存清除
刷新按钮现在会完全清除所有缓存：

```javascript
// 用户点击"🔄 强制刷新"时的执行逻辑
async refreshView() {
    // 1. 清除数据层缓存
    window.DashboardCore.DataLoader.Cache.clear();
    
    // 2. 清除表格层缓存
    delete window.DashboardTableData;
    delete window.DashboardDateBaseData;
    delete window.DashboardDateTableData;
    
    // 3. 重新渲染
    await this.renderDashboard();
}
```

## 🛠️ 开发指南

### 环境要求
- Obsidian 应用
- Dataview 插件
- 支持 ES6+ 的现代浏览器

### 文件结构
```
web3/farm/
├── data/                           # 配置数据
│   ├── risk_percentage.md          # 风险等级配置
│   ├── invest_level_limit.md       # 投资等级配置
│   └── token_prices.md            # 代币价格数据
├── history/                        # 投资历史
│   ├── doing/                      # 进行中项目
│   └── done/                       # 已完成项目
└── lib/                           # 核心库
    └── [8个JavaScript模块]
```

## ⚠️ 模块风险等级

### 🔴 高风险模块 - 谨慎修改
> 这些模块直接影响核心计算逻辑，修改错误会导致数据计算错误

#### `dashboard-calculator.js` 🚨 极高风险
**影响范围**: 所有数值计算、APR、收益、时间范围逻辑

**高风险函数**:
- `calculateTimeRangeEarnings()` - 时间范围收益计算（v3.2.0核心，v3.3.1关键修复）
- `getBalanceAtDateBefore()` - 历史数据插值（v3.3.1优化）
- `calculateUSDTAmount()` - USDT价值计算
- `calculateUSDTBasedAPR()` - APR计算

**⚠️ v3.3.1重要修复**: 
- 修复了追加投资计算的严重错误，影响所有有追加投资的项目
- 修复公式: `timeRangeEarnings = endBalance - startBalance - additionalInvestment`

**修改前必须**:
1. 完全理解现有逻辑和边界条件
2. 准备完整的测试用例
3. 备份当前版本
4. 在测试环境中验证

**常见错误影响**:
- 时间范围逻辑错误 → 日期维度数据完全错误
- 数据插值错误 → 历史分析不准确
- APR计算错误 → 投资决策依据错误

#### `dashboard-table-builder.js` 🚨 极高风险
**影响范围**: 表格聚合、汇总计算、数据展示

**高风险函数**:
- `buildTimeSummaryRow()` - 时间汇总（v3.2.0修复过负数聚合bug）
- `calculateRiskSummary()` - 风险维度聚合
- `buildDateTable()` - 日期维度表格构建

**修改前必须**:
1. 了解聚合逻辑和边界处理
2. 测试正负数混合的场景
3. 验证各个时间粒度的计算
4. 检查缓存一致性

**常见错误影响**:
- 聚合计算错误 → 总计数据错误
- 缓存逻辑错误 → 数据不一致
- 表格结构错误 → 界面崩溃

### 🟡 中等风险模块 - 小心修改

#### `dashboard-data-loader.js` 🟡 中等风险
**影响范围**: 数据加载、解析、缓存管理

**风险点**:
- 价格数据解析错误 → 所有USDT计算错误
- 项目数据验证错误 → 数据丢失或污染
- 缓存逻辑错误 → 性能问题或数据不一致

#### `dashboard-core.js` 🟡 中等风险
**影响范围**: 系统初始化、API接口、缓存管理

**风险点**:
- API接口变更 → 上层调用失败
- 缓存清除逻辑错误 → 数据不更新
- 模块加载顺序错误 → 系统启动失败

### 🟢 低风险模块 - 相对安全

#### `dashboard-ui.js` 🟢 低风险
**影响范围**: 用户界面、交互逻辑

**注意事项**:
- 事件绑定错误 → 界面功能失效
- 样式修改 → 显示异常

#### `dashboard-renderer.js` 🟢 低风险
**影响范围**: HTML渲染、表格显示

#### `dashboard-utils.js` 🟢 低风险
**影响范围**: 工具函数、格式化

#### `dashboard-config.js` 🟢 低风险
**影响范围**: 配置管理、常量定义

## 📋 功能开发映射表

### 想要修改的功能 → 需要修改的文件

#### 💰 收益计算相关
| 需求 | 主要文件 | 次要文件 | 风险级别 |
|------|----------|----------|----------|
| 修改APR计算公式 | `calculator.js` | `table-builder.js` | 🔴 极高 |
| 修改收益聚合逻辑 | `table-builder.js` | `calculator.js` | 🔴 极高 |
| 添加新的收益指标 | `calculator.js` | `table-builder.js`, `renderer.js` | 🔴 高 |
| 修改期望收益计算 | `calculator.js` | `data-loader.js` (风险配置) | 🟡 中等 |
| **当前资产列管理** | `table-builder.js` | `calculator.js` | 🟡 **中等** |
| **债务处理机制** | `calculator.js` | `table-builder.js` | 🟡 **中等** |

#### 📅 时间范围相关
| 需求 | 主要文件 | 次要文件 | 风险级别 |
|------|----------|----------|----------|
| 修改时间范围逻辑 | `calculator.js` | `table-builder.js` | 🔴 极高 |
| 添加新的时间粒度 | `table-builder.js` | `ui.js`, `core.js` | 🟡 中等 |
| 修改数据插值策略 | `calculator.js` | - | 🔴 极高 |
| 调整时间显示格式 | `utils.js` | `table-builder.js` | 🟢 低 |

#### 📊 表格和UI相关
| 需求 | 主要文件 | 次要文件 | 风险级别 |
|------|----------|----------|----------|
| 添加新的表格列 | `table-builder.js` | `renderer.js` | 🟡 中等 |
| 修改表格样式 | `renderer.js` | `ui.js` | 🟢 低 |
| 调整视图切换逻辑 | `ui.js` | `core.js` | 🟢 低 |
| 修改排序逻辑 | `table-builder.js` | - | 🟡 中等 |
| 折叠/展开功能修改 | `ui.js` | `renderer.js`, `table-builder.js` | 🟢 低 |
| 全局控制按钮调整 | `ui.js` | - | 🟢 低 |
| 状态持久化修改 | `ui.js` | - | 🟡 中等 |
| **风险状态切换功能** | `ui.js` | `core.js`, `table-builder.js` | 🟢 **低** |
| **状态过滤逻辑修改** | `table-builder.js` | `data-loader.js` | 🟡 **中等** |

### 📁 数据和缓存相关
| 需求 | 主要文件 | 次要文件 | 风险级别 |
|------|----------|----------|----------|
| 添加新的数据源 | `data-loader.js` | `core.js` | 🟡 中等 |
| 修改缓存策略 | `data-loader.js` | `core.js` | 🟡 中等 |
| 调整价格数据解析 | `data-loader.js` | `calculator.js` | 🔴 高 |
| 修改项目数据格式 | `data-loader.js` | `calculator.js`, `table-builder.js` | 🔴 高 |
| **价格数据格式迁移** | `data-loader.js` | `update_prices.py` | 🔴 **极高** |

#### ⚠️ 价格数据相关修改的特殊风险

**价格数据格式修改** 🚨 **极高风险**
- **影响范围**: 整个系统的数据基础
- **风险点**: 
  - DataviewJS兼容性问题
  - 缓存一致性问题  
  - 多格式并存的冲突
  - 数据丢失风险
- **修改前必须**:
  1. 完整备份现有价格数据
  2. 理解DataviewJS的文件读取限制
  3. 测试新格式的解析能力
  4. 准备回滚方案

**常见价格数据相关错误**:
- 格式检测错误 → 解析器选择错误 → 数据读取失败
- 缓存不一致 → 部分数据更新 → 计算结果错误
- JSON读取失败 → 回退到错误的解析器 → 系统崩溃

## 🔧 修改代码的影响分析

### 核心依赖链分析

#### 📊 数据计算依赖链
```
价格数据解析 → USDT价值计算 → APR计算 → 收益聚合 → 表格显示
    ↓              ↓              ↓           ↓           ↓
data-loader.js → calculator.js → calculator.js → table-builder.js → renderer.js
```

⚠️ **影响分析**: 链条上游的任何修改都会影响下游所有环节

#### 🕐 时间范围依赖链
```
时间范围确定 → 数据插值 → 收益计算 → 时间聚合 → 表格展示
      ↓           ↓          ↓          ↓          ↓
calculator.js → calculator.js → calculator.js → table-builder.js → renderer.js
```

⚠️ **影响分析**: 时间逻辑修改会影响所有日期维度的数据

#### 🗂️ 缓存依赖链
```
数据缓存 → 表格缓存 → 渲染缓存 → 用户界面
    ↓        ↓          ↓          ↓
data-loader.js → core.js → core.js → ui.js
```

⚠️ **影响分析**: 缓存逻辑错误会导致数据不一致或性能问题

### 🚨 关键修改点的连锁反应

#### 修改 `calculateTimeRangeEarnings()` 函数
**直接影响**:
- 所有日期维度的收益计算
- 时间范围APR计算
- 投资天数计算

**间接影响**:
- 日期维度表格的所有数值列
- 月度/周度/年度汇总数据
- 期望收益Gap计算

**测试要点**:
```javascript
// 必须测试的场景
1. 跨时间周期的项目
2. 有追加投资的项目  
3. 负收益的项目
4. 单日数据的项目
5. 数据缺失的项目
```

#### 修改聚合函数（如 `buildTimeSummaryRow()`）
**直接影响**:
- 时间汇总行的所有数值
- 正负收益的处理
- 缓存数据的一致性

**间接影响**:
- 总计行的计算
- 跨时间段的比较分析
- 用户的投资决策依据

**测试要点**:
```javascript
// 必须测试的场景
1. 纯正收益的时间段
2. 纯负收益的时间段
3. 正负收益混合的时间段
4. 空数据的时间段
5. 缓存更新后的一致性
```

## 🧪 开发和测试最佳实践

### 修改前的准备工作

#### 1. 🔍 影响分析检查清单
- [ ] 确定修改的函数在依赖链中的位置
- [ ] 列出所有可能受影响的下游函数
- [ ] 识别相关的缓存机制
- [ ] 确认数据格式和接口契约

#### 2. 🛡️ 风险防护措施
```javascript
// 1. 备份关键文件
cp dashboard-calculator.js dashboard-calculator.js.backup

// 2. 启用详细日志
window.DashboardCore.setConfig('DEBUG.ENABLE_CONSOLE_LOG', true);

// 3. 清除缓存进行测试
window.DashboardCore.API.refreshView();
```

#### 3. 📝 测试用例准备
针对**时间范围计算**的测试用例：
```javascript
// 测试数据准备
const testCases = [
    {
        name: "正常跨月项目",
        projectStart: "2025-05-30",
        projectEnd: "2025-06-15", 
        periodStart: "2025-06-01",
        periodEnd: "2025-06-30",
        expectedDays: 15, // 6月1日-6月15日
        hasAdditionalInvestment: false
    },
    {
        name: "有追加投资的项目", 
        // ... 更多测试场景
    }
];
```

### 修改后的验证流程

#### 1. 🔢 数据一致性检查
```javascript
// 验证风险维度和日期维度的总投资额一致
const riskTotalInvestment = /* 从风险维度获取 */;
const dateTotalInvestment = /* 从日期维度获取 */;
console.assert(riskTotalInvestment === dateTotalInvestment, "投资总额不一致");
```

#### 2. 📊 边界条件测试
- 测试空数据的处理
- 测试单条记录的处理  
- 测试极值数据的处理
- 测试日期边界的处理

#### 3. 🔄 缓存一致性验证
```javascript
// 清除缓存前后的数据应该一致
const beforeCache = getCurrentData();
window.DashboardCore.API.refreshView();
const afterRefresh = getCurrentData();
// 比较 beforeCache 和 afterRefresh
```

### 常见开发任务的详细指导

#### 🆕 添加新的计算指标

**步骤1**: 在 `dashboard-calculator.js` 中添加计算函数
```javascript
// 示例：添加收益率计算
calculateReturnRate(usdtAmounts, investDays) {
    return window.DashboardCore.Utils.Error.safeExecute(() => {
        if (usdtAmounts.investAmount <= 0) return 0;
        const totalReturn = usdtAmounts.withdrawAmount - usdtAmounts.investAmount;
        return (totalReturn / usdtAmounts.investAmount) * 100;
    }, 0, '计算收益率');
}
```

**步骤2**: 在 `dashboard-table-builder.js` 中集成
```javascript
// 在项目详情行中添加新列
const returnRate = window.DashboardCore.DerivedCalculator.calculateReturnRate(usdtAmounts, investDays);
projectRow.push(window.DashboardCore.Utils.Number.formatPercentage(returnRate));
```

**步骤3**: 更新表头
```javascript
// 在表头数组中添加新列
const headers = [
    '风险等级', '投入金额', '当前占比', '项目',
    '仓位状态', '建议操作', '总收益', '收益率', // 新增收益率列
    // ... 其他列
];
```

**步骤4**: 验证和测试
- 检查所有表格行的列数一致性
- 验证新指标的计算准确性
- 测试边界条件（零投资、负收益等）

#### 📅 修改时间范围逻辑

**⚠️ 极高风险操作，需要特别谨慎**

**修改前的完整理解**:
```javascript
// 当前的时间范围逻辑（v3.2.0）
const effectiveStart = projectStart > startDate ? projectStart : startDate;
const effectiveEnd = projectEnd < endDate ? projectEnd : endDate;

// 理解：取项目时间和查询时间的交集
// 如果没有交集（effectiveStart > effectiveEnd），返回零值
```

**修改示例**:
```javascript
// 如果要修改为包含部分重叠的逻辑
calculateTimeRangeEarnings(project, priceMap, startDate, endDate, unit) {
    // 1. 先完全理解原逻辑
    // 2. 设计新逻辑
    // 3. 保留原逻辑作为备份
    // 4. 逐步实现新逻辑
    // 5. 全面测试各种场景
}
```

**必须测试的场景**:
1. 项目完全在时间范围内
2. 项目完全在时间范围外  
3. 项目开始在范围前，结束在范围内
4. 项目开始在范围内，结束在范围后
5. 项目跨越整个时间范围
6. 边界日期重合的情况

### 调试技巧

#### 启用调试日志
```javascript
// 在浏览器控制台中运行
window.DashboardCore.setConfig('DEBUG.ENABLE_CONSOLE_LOG', true);
```

#### 检查缓存状态
```javascript
// 查看当前缓存
console.log('数据缓存:', window.DashboardCore.DataLoader.Cache);
console.log('表格缓存:', window.DashboardTableData);
console.log('日期缓存:', window.DashboardDateTableData);
```

#### 强制重新计算
```javascript
// 清除特定缓存
delete window.DashboardDateTableData['monthly'];

// 清除所有缓存
window.DashboardCore.API.refreshView();
```

## 🔨 实际开发场景和解决方案

### 场景1：用户报告收益计算不准确

#### 🔍 排查步骤
1. **确定问题范围**
   ```javascript
   // 检查是单个项目还是系统性问题
   console.log('风险维度总收益:', /* 从风险表格提取 */);
   console.log('日期维度总收益:', /* 从日期表格提取 */);
   ```

2. **检查数据源**
   ```javascript
   // 验证价格数据
   console.log('价格数据:', window.DashboardCore.DataLoader.Cache.get('priceMap'));
   
   // 验证项目数据
   console.log('项目历史:', project.file.lists);
   ```

3. **逐层追踪计算**
   ```javascript
   // 在 calculator.js 中添加调试日志
   calculateTimeRangeEarnings(project, priceMap, startDate, endDate, unit) {
       console.log('计算参数:', { project: project.file.name, startDate, endDate, unit });
       const result = /* 计算逻辑 */;
       console.log('计算结果:', result);
       return result;
   }
   ```

#### ⚠️ 高风险修复点
- `calculateUSDTAmount()` - 价格计算错误会影响所有收益
- `calculateTimeRangeEarnings()` - 时间范围错误会影响日期维度
- `buildTimeSummaryRow()` - 聚合错误会影响汇总数据

### 场景2：添加新的风险等级

#### 📝 修改清单
1. **数据配置** (🟢 低风险)
   ```markdown
   # 在 data/risk_percentage.md 中添加
   - [risk:: 5] [percent:: 0.05] [cn:: 实验性] [expect_apr:: 5]
   ```

2. **颜色配置** (🟢 低风险)
   ```javascript
   // 在 dashboard-config.js 中更新颜色映射
   RISK_COLORS: {
       1: '#28a745', // 低风险 - 绿色
       2: '#ffc107', // 中风险 - 黄色  
       3: '#fd7e14', // 高风险 - 橙色
       4: '#dc3545', // 极高风险 - 红色
       5: '#6f42c1'  // 实验性 - 紫色
   }
   ```

3. **验证逻辑** (🟡 中等风险)
   ```javascript
   // 确保所有风险等级验证函数支持新等级
   // 在 dashboard-utils.js 中检查 isValidRiskLevel()
   ```

#### 🧪 测试要点
- 新风险等级的项目能正确分组
- 颜色显示正确
- 百分比计算正确
- 期望APR应用正确

### 场景3：修改表格列结构

#### ⚠️ 高风险操作 - 容易导致列数不匹配

**正确的修改流程**：

1. **统计当前列数**
   ```javascript
   // 记录所有表格构建函数的列数
   buildRiskSummaryRow() // 17列
   buildProjectRow()     // 17列  
   buildTotalRow()       // 17列
   buildTimeSummaryRow() // 17列
   buildDateProjectRow() // 17列
   ```

2. **同步修改所有相关函数**
   ```javascript
   // 必须同时修改的地方：
   - 表头数组定义
   - 风险维度的所有行构建函数
   - 日期维度的所有行构建函数
   - 缓存清除后的重新构建
   ```

3. **验证列数一致性**
   ```javascript
   // 在每个构建函数中添加验证
   const row = [...]; // 构建表格行
   if (row.length !== expectedColumnCount) {
       console.error(`列数不匹配: 期望${expectedColumnCount}，实际${row.length}`);
   }
   ```

### 场景4：性能优化 - 大数据集处理

#### 🐌 性能瓶颈识别
```javascript
// 添加性能监控
const startTime = performance.now();
const result = expensiveCalculation();
const endTime = performance.now();
console.log(`计算耗时: ${endTime - startTime}ms`);
```

#### 🚀 优化策略

1. **缓存优化** (🟡 中等风险)
   ```javascript
   // 调整缓存时间
   window.DashboardCore.setConfig('PERFORMANCE.CACHE_DURATION', 600000); // 10分钟
   
   // 分层缓存
   window.DashboardCore.setConfig('PERFORMANCE.ENABLE_CACHE', true);
   ```

2. **数据分页** (🟢 低风险)
   ```javascript
   // 在 table-builder.js 中实现分页
   const pageSize = 50;
   const pagedData = allData.slice(page * pageSize, (page + 1) * pageSize);
   ```

3. **延迟加载** (🟡 中等风险)
   ```javascript
   // 只加载当前视图需要的数据
   if (viewType === 'risk') {
       // 只加载风险维度需要的计算
   } else {
       // 只加载日期维度需要的计算
   }
   ```

## 🚨 关键错误模式和预防

### 错误模式1：缓存不一致

#### 症状
- 刷新后数据不更新
- 不同视图显示不同的总计
- 修改代码后看不到效果

#### 根本原因
```javascript
// 错误：只清除了部分缓存
delete window.DashboardDateTableData['monthly'];
// 但没有清除 window.DashboardTableData

// 错误：修改了计算逻辑但没有清除缓存
calculateTimeRangeEarnings() // 修改了这个函数
// 但缓存的表格数据仍然是旧的
```

#### 预防措施
```javascript
// 正确：清除所有相关缓存
function clearAllRelatedCache() {
    window.DashboardCore.DataLoader.Cache.clear();
    delete window.DashboardTableData;
    delete window.DashboardDateBaseData;
    delete window.DashboardDateTableData;
}

// 在每次修改计算逻辑后调用
clearAllRelatedCache();
```

### 错误模式2：时间范围边界错误

#### 症状
- 某些时间段的数据异常
- 跨月项目计算错误
- 投资天数不符合预期

#### 根本原因
```javascript
// 错误：没有正确处理边界情况
if (effectiveStart > effectiveEnd) {
    return 0; // 简单返回0，但没有考虑后续影响
}

// 错误：日期比较逻辑错误
if (entryDate > targetDate) { // 应该是 >=
    // 边界日期处理错误
}
```

#### 预防措施
```javascript
// 正确：详细的边界条件处理
function calculateTimeRangeEarnings(project, priceMap, startDate, endDate, unit) {
    // 1. 输入验证
    if (!startDate || !endDate || startDate > endDate) {
        console.warn('无效的时间范围:', { startDate, endDate });
        return { timeRangeEarnings: 0, totalInvestment: 0, /* ... */ };
    }
    
    // 2. 边界情况处理
    if (effectiveStart > effectiveEnd) {
        console.log('项目与时间范围无交集:', { 
            project: project.file.name, 
            effectiveStart, 
            effectiveEnd 
        });
        return { timeRangeEarnings: 0, totalInvestment: 0, /* ... */ };
    }
    
    // 3. 详细日志
    console.log('时间范围计算:', {
        project: project.file.name,
        originalStart: startDate,
        originalEnd: endDate,
        effectiveStart,
        effectiveEnd,
        days: effectiveInvestDays
    });
}
```

### 错误模式4：价格数据格式兼容性问题

#### 症状
- Dashboard显示"价格数据未找到"
- 系统无法读取价格文件
- 价格数据更新后不生效
- 不同格式的价格文件冲突

#### 根本原因
```javascript
// 错误：使用DataviewJS读取JSON文件
const jsonPage = dv.page('web3/farm/data/token_prices.json');
// DataviewJS无法解析JSON文件，返回undefined

// 错误：自定义YAML解析器处理边界情况
function parseCompactYAML(content) {
    const lines = content.split('\n');
    // 复杂的字符串处理，容易出错
}

// 错误：格式检测逻辑不准确
if (format === 'compact') {
    // 可能选择错误的解析器
}
```

#### 预防措施
```javascript
// 正确：使用frontmatter格式，利用DataviewJS原生解析
function parsePriceDataFrontmatter(pricesPage) {
    // 直接从DataviewJS解析的frontmatter中获取
    const prices = pricesPage.prices;
    // 不需要自定义解析逻辑
}

// 正确：明确的格式检测和回退机制
function loadPriceData() {
    try {
        // 优先使用frontmatter格式
        const frontmatterData = this.parsePriceDataFrontmatter(pricesPage);
        if (frontmatterData.size > 0) {
            return frontmatterData;
        }
    } catch (e) {
        console.warn('frontmatter解析失败，回退到列表格式:', e);
    }
    
    // 回退到传统列表格式
    return this.parsePriceDataList(pricesPage);
}

// 正确：强制刷新时清除价格相关缓存
function refreshPriceData() {
    // 清除数据层缓存
    window.DashboardCore.DataLoader.Cache.clear();
    
    // 清除表格层缓存
    delete window.DashboardTableData;
    delete window.DashboardDateBaseData;
    delete window.DashboardDateTableData;
    
    console.log('✅ 所有价格相关缓存已清除');
}
```

#### 价格数据格式最佳实践
```yaml
# 推荐的frontmatter格式
---
provider: Binance & CoinGecko (Historical)
last_updated: 2025-07-12 06:54:40
format: frontmatter
prices:
  BTC:
    2025-05-10: 104809.53
    2025-05-13: 104103.72
  SOL:
    2025-05-29: 166.71
---

# Token Historical Prices

价格数据存储在frontmatter中，DataviewJS可直接访问。
```

### 错误模式3：数值聚合错误

#### 症状
- 总计不等于明细之和
- 负数被意外排除
- 精度丢失导致的累积误差

#### 根本原因
```javascript
// 错误：过滤逻辑错误
const total = projects.reduce((sum, p) => {
    if (p.earnings > 0) { // 错误：排除了负收益
        return sum + p.earnings;
    }
    return sum;
}, 0);

// 错误：精度处理不当
const apr = Math.round(rawAPR); // 过早四舍五入
```

#### 预防措施
```javascript
// 正确：包含所有有效数据
const total = projects.reduce((sum, p) => {
    if (p.earnings !== -1 && !isNaN(p.earnings)) { // 只排除无效数据
        return sum + p.earnings;
    }
    return sum;
}, 0);

// 正确：最后进行精度处理
const rawTotal = projects.reduce(/* ... */);
const formattedTotal = Math.round(rawTotal * 100) / 100; // 最后四舍五入
```

## 📚 开发者速查手册

### 常用调试命令
```javascript
// 查看当前缓存状态
console.log('数据缓存:', window.DashboardCore.DataLoader.Cache._cache);
console.log('表格缓存:', window.DashboardTableData);
console.log('日期缓存:', window.DashboardDateTableData);

// 价格数据专项调试 (新增)
console.log('价格文件格式:', window.DashboardCore.dv.page('web3/farm/data/token_prices').format);
console.log('价格数据结构:', window.DashboardCore.dv.page('web3/farm/data/token_prices').prices);
console.log('BTC价格数据:', window.DashboardCore.dv.page('web3/farm/data/token_prices').prices?.BTC);

// 强制刷新特定缓存
delete window.DashboardDateTableData['monthly'];
window.DashboardCore.API.refreshView();

// 启用详细日志
window.DashboardCore.setConfig('DEBUG.ENABLE_CONSOLE_LOG', true);

// 查看当前视图状态
console.log('视图状态:', window.DashboardCore.ViewState);
console.log('当前风险状态过滤:', window.DashboardCore.ViewState.riskProjectStatus); // v4.2.0新增

// 手动切换风险状态 (v4.2.0新增)
await window.DashboardCore.API.switchRiskProjectStatus('Done');   // 切换到Done状态
await window.DashboardCore.API.switchRiskProjectStatus('Doing');  // 切换到Doing状态

// 手动触发计算验证
const testProject = /* 选择一个测试项目 */;
const result = window.DashboardCore.USDTCalculator.calculateTimeRangeEarnings(
    testProject, priceMap, '2025-06-01', '2025-06-30', 'SOL'
);
console.log('计算结果:', result);

// v3.3.1 验证追加投资计算修复
// 对于LP SOL项目6月数据，应该显示：
// timeRangeEarnings: 约130.04 USDT (而非之前的795.72)
console.log('LP SOL 6月收益修复验证:', result.timeRangeEarnings);

// 折叠/展开状态调试 (v3.3.0新增)
console.log('展开状态:', window.DashboardCore.CollapseState.getAllStates());

// 手动控制展开/收起
window.DashboardCore.UI.expandAllTimeGroups();   // 展开所有
window.DashboardCore.UI.collapseAllTimeGroups(); // 收起所有

// 清除折叠状态缓存
window.DashboardCore.CollapseState.clearAllStates();
```

### 价格数据更新和维护
```bash
# 推荐的价格数据更新工作流
cd web3/farm

# 1. 备份现有价格数据
cp data/token_prices.md data/token_prices.md.backup

# 2. 更新历史价格
python update_prices.py --mode historical

# 3. 检查生成的文件格式
head -20 data/token_prices.md

# 4. 验证frontmatter格式正确
# 应该看到 "format: frontmatter" 和正确的YAML结构

# 5. 可选：更新实时价格
python update_prices.py --mode realtime

# 6. 验证特定代币价格
python update_prices.py --mode historical --tokens BTC --dates 2025-05-10

# 常见故障排除
# 如果价格数据不生效：
# 1. 检查文件格式是否为frontmatter
# 2. 在dashboard中强制刷新
# 3. 检查浏览器控制台错误
```

### 模块间接口契约
```javascript
// calculator.js 输出格式
{
    timeRangeEarnings: number,    // 时间范围收益
    totalInvestment: number,      // 总投资金额
    additionalInvestment: number, // 追加投资
    effectiveInvestDays: number,  // 有效投资天数
    timeRangeAPR: number,         // 时间范围APR
    effectiveStart: string,       // 有效开始日期
    effectiveEnd: string,         // 有效结束日期
    priceFound: boolean          // 价格数据是否找到
}

// table-builder.js 期望输入格式
{
    project: Object,              // 项目对象
    amountInUsdt: number,        // USDT投资金额
    usdtTotalEarned: number,     // USDT总收益
    investDays: number,          // 投资天数
    projectApr: number,          // 项目APR
    riskLevel: number,           // 风险等级
    expectApr: number            // 期望APR
}
```

### 紧急修复检查清单
- [ ] 备份当前工作版本
- [ ] 识别修改的风险等级
- [ ] 清除所有相关缓存
- [ ] 验证基本功能正常
- [ ] 检查控制台错误信息
- [ ] 测试多种数据场景
- [ ] 确认数据一致性
- [ ] 记录修改内容

## 🚨 故障排除

### 常见问题

#### 1. DashboardCore.API 未定义
**症状**: 页面显示 "DashboardCore.API 未定义" 错误

**原因**: `dashboard-core.js` 文件丢失或加载失败

**解决方案**:
```bash
# 检查文件是否存在
ls -la web3/farm/lib/dashboard-core.js

# 如果文件丢失，从备份恢复
cp backup/lib_bak_*/dashboard-core.js web3/farm/lib/
```

#### 2. 数据更新后不显示最新结果
**症状**: 修改数据文件后，表格显示的仍是旧数据

**原因**: 缓存机制导致

**解决方案**:
1. 点击 "🔄 强制刷新" 按钮
2. 或在控制台运行: `window.DashboardCore.API.refreshView()`

#### 3. 表格数据计算错误
**症状**: 收益、APR等计算结果不正确

**调试步骤**:
1. 检查原始数据格式是否正确
2. 查看浏览器控制台的错误信息
3. 验证价格数据是否完整
4. 确认日期格式是否标准

#### 4. 模块加载失败
**症状**: 系统提示缺少某个模块

**解决方案**:
1. 检查 `lib/` 目录下所有8个JS文件是否存在
2. 确认文件权限正确
3. 查看文件内容是否完整（无语法错误）

### 性能优化

#### 大数据集处理
- 启用缓存: `PERFORMANCE.ENABLE_CACHE = true`
- 调整缓存时间: `PERFORMANCE.CACHE_DURATION = 300000` (5分钟)
- 分页显示: 使用内置的分页功能

#### 内存管理
- 定期清除缓存: 使用强制刷新功能
- 避免内存泄漏: 确保事件处理器正确绑定和解绑

## 📚 版本历史

### v4.3.1 (2025-07-13) - 日维度数据计算逻辑修复
**🚨 重要Bug修复**:
- 🐛 **日维度计算错误修复**: 修复日维度"日收益"和"当前APR"显示为0的严重问题
- 📊 **数据来源纠正**: 日维度现在正确使用项目总体数据而非单日数据进行计算
- ✅ **列含义实现**: 真正实现v4.3.0设计中的"日收益=项目累计平均日收益"逻辑
- 🔧 **逻辑一致性**: 确保日维度的APR和收益计算与设计文档完全一致

**问题根源分析**:
```javascript
// 修复前（错误）：使用时间范围数据（单日数据）
const projectApr = calculateUSDTBasedAPR(timeRangeUsdtAmounts, timeRangeInvestDays);
// 结果：同一天价格相同 → 收益=0，投资天数=1 → APR=0

// 修复后（正确）：使用项目总体数据
const projectTotalHistoryData = calculateProjectHistory(project, priceMap);
const projectApr = calculateUSDTBasedAPR(projectTotalUsdtAmounts, projectTotalInvestDays);
// 结果：完整项目收益 → 收益=9764 USDT，投资天数=103 → APR=149.5%
```

**技术实现**:
- 在`buildDateTable()`函数中添加项目总体数据计算
- 通过`granularity === 'daily'`条件精确控制修复范围
- 保证周/月/年维度继续使用时间范围逻辑，不受影响
- 保证风险维度完全不受影响，数据计算独立

**修复效果**:
- **20250201-Spot.Trade.BTC**项目在日维度正确显示：
  - 日收益：94.80 USDT/天 ✅（修复前：0）
  - 当前APR：149.5% ✅（修复前：0%）
- 其他维度数据完全不受影响，保持原有计算逻辑

**影响范围控制**:
- ✅ **风险维度**：使用独立计算路径，完全不受影响
- ✅ **周/月/年维度**：通过条件判断保护，继续使用时间范围逻辑
- ✅ **日维度**：精确修复，使用项目总体数据
- ✅ **代码安全性**：修改通过精确的条件判断进行隔离

### v4.3.0 (2025-07-13) - 日维度列设计优化
**日期维度用户体验改进**:
- ✨ **日维度总收益列**: 改为显示"当日 vs 前一日"的价值变化，提供真实的日间增量分析
- 🔧 **日收益列统一**: 所有维度（日/周/月）的日收益列均显示平均日收益，保持逻辑一致性
- 📊 **列含义明确**: 总收益列在不同维度有专门含义，日收益列在所有维度保持统一标准
- 📚 **文档完善**: 在README中详细记录日期维度的列设计理念和计算逻辑

**技术实现**:
- 新增 `calculateDailyEarnings()` 函数实现日维度的当日vs前一日比较
- 新增 `getProjectValueAtDate()` 函数获取项目在指定日期的总价值
- 修改日维度的"日收益"列计算，使用项目累计平均日收益而非日变化
- 保持周维度和月维度的原有逻辑不变

**设计理念**:
- 日维度：总收益=日间变化，日收益=累计平均
- 周/月维度：总收益=期间总收益，日收益=期间平均
- 既满足日间趋势分析需求，又保持维度间的逻辑一致性

### v4.2.0 (2025-07-12) - 风险视图状态切换功能
**🎯 项目状态管理优化**:
- 🆕 **Done/Doing状态切换**: 风险维度视图新增项目状态过滤功能
- 🔀 **界面一致性**: 状态切换按钮与日期粒度切换保持相同的设计风格
- 📊 **独立分析**: 支持独立分析Doing和Done项目的风险分布和收益表现
- 🎨 **状态标识**: 清晰的按钮状态指示，当前选中状态高亮显示

**🔧 技术实现**:
- **状态管理**: 在`ViewState`中新增`riskProjectStatus`字段
- **数据过滤**: `buildRiskTable()`方法支持按状态过滤项目数据
- **API扩展**: 新增`switchRiskProjectStatus()`切换方法
- **缓存优化**: 状态切换时自动刷新相关缓存数据

**💡 用户价值**:
- 📈 **完整项目追踪**: 不仅关注当前投资，也能分析历史投资表现
- 🎯 **风险复盘**: 通过Done项目分析，评估历史风险决策的有效性
- 📊 **对比分析**: 比较Doing和Done项目在不同风险等级的表现差异
- 🚀 **决策优化**: 基于历史数据优化未来的风险配置策略

**🎨 界面功能**:
```
风险维度视图控制区：
[风险维度] [日期维度]  状态: [Doing] [Done]  🔄 强制刷新
```

**使用场景**:
- **Doing状态**: 查看当前活跃投资的风险分布和资产配置
- **Done状态**: 分析已完成项目的历史表现和风险回报
- **策略制定**: 基于Done项目的成功率调整当前Doing项目的风险分配

### v4.1.0 (2025-07-12) - 当前资产列功能与界面优化
**💰 当前资产管理功能**:
- 🆕 **新增当前资产列**: 在风险维度视图中显示真实净资产状况
- 💳 **债务处理机制**: 支持债务字段解析，自动从当前资产中扣除
- 🎨 **债务可视化**: 债务金额以红色小字显示，便于风险识别
- 🧮 **计算公式**: `当前资产 = 投入金额 + 总收益 - 债务金额`

**🎯 界面设计优化**:
- ✅ **风险视图**: 保留当前资产列，用于资产配置和总体资产管理
- ✅ **日期视图**: 移除当前资产列，专注于时间段分析和收益表现
- 🎨 **颜色规范**: 当前资产正常显示，只有债务信息使用红色提示
- 📊 **表格结构**: 风险维度18列，日期维度17列，各司其职

**🔧 技术实现**:
- **债务字段解析**: 支持 "16690 USDT" 格式的债务数据
- **数值安全验证**: 防止NaN传播，确保计算稳定性
- **智能数据源选择**: 根据数据有效性选择合适的计算来源
- **模块化设计**: 在不同维度中采用不同的数据处理策略

**💡 用户价值**:
- 📈 **资产净值透明**: 清晰了解扣除债务后的真实资产状况
- 🎯 **功能聚焦**: 风险视图关注资产配置，日期视图关注收益分析
- 🔍 **债务风控**: 直观显示债务信息，支持风险管理决策
- 🚀 **决策支持**: 基于真实净资产进行投资决策

### v4.0.0 (2025-07-12) - 统一价值公式重大升级
**🚀 突破性改进**:
- ⚡ **统一收益计算公式**: 实现所有项目类型的一致性收益计算
- 🎯 **Trade项目收益修正**: 现货交易项目现在正确显示价格收益
- 📈 **DeFi项目收益增强**: 同时包含数量收益和价格收益，更全面反映投资表现
- 🔧 **APR计算优化**: 所有APR相关列基于统一的收益数据，确保数据一致性

**核心突破 - 统一价值公式**:
```javascript
收益 = (最终价格 × 最终数量) - (初始价格 × 初始数量) - 追加投资价值
```

**具体改进**:
- **Trade项目**: 
  - BTC项目收益: $0 → $9,811.51 ✅
  - SOL项目收益: $0 → $9,295.00 ✅  
  - SUI项目收益: $0 → $1,153.00 ✅
- **DeFi项目**:
  - 增加价格收益分量，APR平均提升30-50%
  - 更准确反映代币价格变化对投资回报的影响
- **合约项目**:
  - 完整支持U本位和币本位合约的价值计算
  - 正确处理头寸价值变化

**技术架构优化**:
- **数学一致性**: 所有项目类型使用相同的价值计算逻辑
- **经济学正确性**: 收益计算符合真实投资表现
- **实施简洁性**: 无需区分项目类型，统一计算框架
- **向后兼容**: 现有数据结构完全兼容，平滑升级

**影响范围**:
- 🎨 所有收益相关列 (总收益、日收益、期望收益等)
- 📊 所有APR相关列 (当前APR、APR Gap、组合APR等) 
- 📈 风险维度和日期维度的汇总统计
- 💹 投资决策分析的准确性大幅提升

**适用场景扩展**:
- ✅ 现货交易 (完美支持价格收益)
- ✅ DeFi Farming (数量+价格双重收益)
- ✅ U本位合约 (USDT计价收益)
- ✅ 币本位合约 (币种价值变化)
- ✅ 稳定币项目 (纯数量收益)

### v3.3.1 (2025-07-11) - 时间范围收益计算修复
**🚨 关键Bug修复**:
- 🐛 **追加投资计算错误修复**: 修复时间范围收益计算中最严重的逻辑错误
- 📊 **数据获取优化**: 优化 `getBalanceAtDateBefore()` 函数，优先使用精确日期匹配
- ✅ **收益准确性提升**: 确保所有有追加投资的项目收益计算正确
- 🔍 **价格数据诊断增强**: 精确显示缺失的价格日期，便于数据维护

**具体修复内容**:
- **核心问题**: `calculateTimeRangeEarnings()` 函数中收益计算公式错误
  ```javascript
  // 修复前（错误）
  const timeRangeEarnings = endBalance - startBalance;
  
  // 修复后（正确）  
  const timeRangeEarnings = endBalance - startBalance - additionalInvestment;
  ```
- **影响案例**: LP SOL项目6月收益从错误的795.72 USDT修正为130.04 USDT
- **价格查找策略**: 改为精确日期匹配，不使用可能偏差很大的历史价格插值
- **诊断信息优化**: 显示具体缺失的价格日期，如"SOL 2025-05-31 价格数据未找到"

**技术细节**:
- 修复了所有日期维度中有追加投资项目的收益高估问题
- 确保收益计算公式遵循: `结束余额 - 起始余额 - 追加投资`
- 新增 `getPriceAtExactDate()` 函数，只进行精确日期匹配
- 在返回结果中包含 `missingPriceDate` 和 `missingPriceToken` 信息
- 表格渲染时显示具体缺失的价格信息，便于用户更新价格数据

**影响范围**: 
- 所有日期维度的收益计算
- 月度/季度汇总数据的准确性
- 投资决策分析的可靠性
- 价格数据维护效率

### v3.3.0 (2025-07-11) - 日期视图折叠/展开功能
**用户体验优化**:
- ✨ **项目详情折叠**: 日期视图默认折叠项目详情，只显示时间汇总
- 🎛️ **展开/收起控制**: 每个时间段支持独立的展开/收起操作
- 📂 **全局控制按钮**: 新增"全部展开"和"全部收起"功能
- 💾 **状态持久化**: 展开/收起状态保存到本地存储，刷新页面后保持
- 🎨 **界面优化**: 功能区布局调整，支持自适应换行
- 🔧 **Markdown修复**: 修复时间显示中的星号问题

**技术实现**:
- 时间汇总行添加可点击的展开/收起按钮（▶️/▼️）
- 项目详情行通过CSS类控制显示/隐藏
- 状态管理器跟踪每个时间段的展开状态
- 响应式布局确保不同屏幕尺寸的良好体验

**模块更新**:
- `dashboard-table-builder.js`: 添加展开按钮和行分类标识
- `dashboard-renderer.js`: 新增折叠/展开CSS样式和动画
- `dashboard-ui.js`: 实现交互逻辑和状态管理
- UI控制区域布局优化，支持功能按钮的灵活排列

### v3.2.0 (2025-07-11) - 时间范围逻辑重构
**重大更新**:
- ✨ **统一时间范围逻辑**: 日期维度真正成为时间段分析工具
- 🔧 **重写时间范围收益计算**: `calculateTimeRangeEarnings()` 函数完全重构
- 📊 **历史数据插值**: 新增 `getBalanceAtDateBefore()` 函数，只使用历史数据
- 💰 **追加投资处理**: 追加投资计入总投资，而非从收益扣除
- 🐛 **负数收益聚合修复**: 修复月度汇总排除负收益的bug
- 🔄 **增强缓存清除**: 强制刷新按钮现在真正清除所有缓存
- 📝 **完善日志输出**: 添加详细的操作反馈

**技术改进**:
- 有效时间范围计算: `max(projectStart, periodStart)` 到 `min(projectEnd, periodEnd)`
- 时间范围APR计算: 基于实际投资天数和收益
- 数据插值策略: 避免使用未来数据，保证分析准确性
- 聚合逻辑统一: 所有汇总函数正确处理负数值

### v3.1.0 - 模块化重构
- 🏗️ **模块化架构**: 拆分为8个独立模块
- ⚡ **性能优化**: 实现多层缓存机制
- 🎨 **UI改进**: 响应式设计和交互优化

### v3.0.0 - 核心系统
- 📊 **双维度分析**: 风险维度和日期维度视图
- 🧮 **计算引擎**: 完整的APR和收益计算
- 🔌 **模块化设计**: 可扩展的架构设计

## 📞 技术支持

### 开发团队联系方式
- **主要开发者**: Claude AI Assistant
- **技术架构**: 模块化JavaScript + Obsidian DataviewJS
- **更新频率**: 根据用户需求持续迭代

### 贡献指南
1. 遵循现有的代码风格和架构模式
2. 添加适当的错误处理和日志输出
3. 更新相关文档和注释
4. 测试所有修改的功能

### 相关资源
- [Obsidian 官方文档](https://help.obsidian.md/)
- [Dataview 插件文档](https://blacksmithgu.github.io/obsidian-dataview/)
- [JavaScript ES6+ 语法参考](https://developer.mozilla.org/en-US/docs/Web/JavaScript)

---

**最后更新**: 2025-07-13  
**文档版本**: v4.3.1  
**维护状态**: 活跃维护中 🚀