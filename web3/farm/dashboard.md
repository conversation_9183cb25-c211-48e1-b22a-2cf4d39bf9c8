---
color: ""
sticker: emoji//1f4b0
---
# Web3 Farm Dashboard

## 📊 实时数据仪表板

```dataviewjs
// ===== Web3 Farm Dashboard 模块化系统 =====
console.log('开始加载 Dashboard 系统...');

// 清空容器
dv.container.innerHTML = '';

try {
    // ===== 加载所有库模块 =====
    console.log('1. 加载配置模块...');
    await dv.view("web3/farm/lib/dashboard-config");

    console.log('2. 加载工具模块...');
    await dv.view("web3/farm/lib/dashboard-utils");

    console.log('3. 加载数据加载模块...');
    await dv.view("web3/farm/lib/dashboard-data-loader");

    console.log('4. 加载计算模块...');
    await dv.view("web3/farm/lib/dashboard-calculator");

    console.log('5. 加载表格构建模块...');
    await dv.view("web3/farm/lib/dashboard-table-builder");

    console.log('6. 加载渲染模块...');
    await dv.view("web3/farm/lib/dashboard-renderer");

    console.log('7. 加载UI模块...');
    await dv.view("web3/farm/lib/dashboard-ui");

    console.log('8. 加载核心API模块...');
    await dv.view("web3/farm/lib/dashboard-core");

    // 等待模块初始化
    await new Promise(resolve => setTimeout(resolve, 100));

    console.log('9. 所有库模块加载完成');

    // ===== 初始化系统 =====
    console.log('10. 初始化 Dashboard 系统...');

    // 验证核心模块
    if (typeof window.DashboardCore === 'undefined') {
        throw new Error('DashboardCore 库未加载');
    }

    if (!window.DashboardCore.API) {
        throw new Error('DashboardCore.API 未定义');
    }

    // 初始化配置
    const dashboardConfig = {
        HISTORY_PATH: '"web3/farm/history"',
        RISK_LIMIT_PATH: 'web3/farm/data/risk_percentage',
        PRICES_FILE_PATH: 'web3/farm/data/token_prices',
        LEVEL_LIMIT_PATH: 'web3/farm/data/invest_level_limit'
    };

    await window.DashboardCore.API.initialize(dv, dashboardConfig);

    // ===== 渲染仪表板 =====
    console.log('11. 渲染完整仪表板...');

    const dashboardHTML = await window.DashboardCore.API.renderDashboard('dashboard-container');

    // 输出到容器
    dv.container.innerHTML = dashboardHTML;

    // 绑定事件处理器
    window.DashboardCore.UI.bindEvents();

    console.log('✅ Dashboard 系统加载完成');

} catch (error) {
    console.error('❌ Dashboard 系统加载失败:', error);

    dv.container.innerHTML = `
        <div style="color: red; padding: 20px; border: 1px solid red; border-radius: 6px; margin: 20px;">
            <h3>❌ Dashboard 系统加载失败</h3>
            <p><strong>错误信息:</strong> ${error.message}</p>
            <p><strong>错误详情:</strong> ${error.stack || '无详细信息'}</p>
            <hr>
            <p><strong>可能的解决方案:</strong></p>
            <ul>
                <li>检查 web3/farm/lib/ 目录下的所有 JS 文件是否存在</li>
                <li>确认数据文件路径配置是否正确</li>
                <li>查看浏览器开发者工具的控制台获取更多错误信息</li>
                <li>尝试刷新页面重新加载</li>
            </ul>
            <hr>
            <p><strong>调试信息:</strong></p>
            <ul>
                <li>当前文件路径: ${dv.current().file.path}</li>
                <li>DashboardCore 存在: ${typeof window.DashboardCore !== 'undefined'}</li>
                <li>可用模块: ${Object.keys(window.DashboardCore || {}).join(', ')}</li>
                <li>Dataview 版本: ${dv.version || '未知'}</li>
            </ul>
        </div>
    `;
}
```

---

> **⚠️ 重要说明**
>
> 这是一个重构后的 Dashboard 系统，使用了模块化的 JavaScript 库架构。
>
> **开发者注意事项：**
> - 所有业务逻辑都已移至 `lib/` 目录下的独立模块中
> - 修改功能时请编辑对应的库文件，而不是这个入口文件
> - 详细的开发文档请参考 [[README_dashboard]]
## 📚 相关文档

- [[README_dashboard]] - 开发者文档和架构说明
- [[web3/farm/data/risk_percentage]] - 风险等级配置
- [[web3/farm/data/invest_level_limit]] - 投资等级配置
- [[web3/farm/data/token_prices]] - 代币价格数据

## 🔧 故障排除

如果 Dashboard 无法正常显示，请检查：

1. **文件结构**：确保 `web3/farm/lib/dashboard-main.js` 文件存在
2. **数据文件**：确保所有配置文件都在正确的位置
3. **Dataview 插件**：确保 Dataview 插件已启用且版本兼容
4. **浏览器控制台**：查看是否有 JavaScript 错误

## 🚀 功能特性

- **风险维度视图**：按风险等级分组显示项目
- **日期维度视图**：按时间维度（日/周/月/年）分析项目表现
- **实时计算**：APR、收益、投资天数等指标的实时计算
- **交互式界面**：支持视图切换和数据筛选
- **响应式设计**：适配不同屏幕尺寸
