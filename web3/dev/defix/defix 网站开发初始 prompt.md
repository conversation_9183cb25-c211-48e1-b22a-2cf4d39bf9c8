我需要在 supabase 上构建一个关于项目调研报告的网站，功能如下：
1. 访客默认进入调研报告列表页，分页展示已有的调研报告列表；
2. 列表中展示调研的项目名、创建时间、更新时间、创建人等信息，显示「查看分析页面」、「查看分析报告」两个操作；
	1. 用户点击列表中的某一项的「查看分析页面」进入项目详情页，读取该项目调研报告的交互网页代码文件，并在页面中渲染显示；
	2. 用户点击列表中的某一项的「查看分析报告」进入项目详情页，读取该项目调研报告的文件路径（md 文件），并在页面中显示该文件内容（渲染 md 文件）；
3. 列表上方提供：
	1. 搜索框，搜索调研报告，根据项目名模糊查询，支持大小写模糊查询；
	2. 「找不到项目？」超链接，点击显示信息收集弹窗，收集用户邮箱、想要找的项目名以及官网主页链接，页面上方显示「请提交你想要调研的项目，算力因素报告产出可能会有延迟，完成后我们会通过邮箱通知，谢谢！」
		1. 用户点击提交后将信息记录到数据库，并通知维护方；
		2. 维护方访问后台待处理列表，上传生成的调研报告后，邮件通知提交的用户报告已生成完毕并附带访问链接。
4. 
5. 新增一份调研报告，包括 id（自增）、创建时间（自动生成）、更新时间（自动生成）、创建人、更新人、是否删除、项目名、调研报告文件路径、交互网页代码文件路径；
6. 删除一份调研报告，根据 id 删除；
7. 更新一份调研报告，根据 id 更新项目名、调研报告文件路径、交互网页代码文件路径；
8. 
9. 查看一份调研报告，根据 id 查看项目名、调研报告文件路径、交互网页代码文件路径，其中两个文件路径在前端展示为超链接形式；
10. 点击报告文件超链接，显示 word 文件预览，并提供下载功能；
11. 点击代码文件路径，直接读取并渲染其中的 html 到前端页面中。
