/Users/<USER>/Library/Mobile Documents/iCloud~md~obsidian/Documents/Guantik/三件套信息总结AI助手技术方案.md

# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## Repository Overview

This is an Obsidian vault focused on Web3 DeFi (Decentralized Finance) farming and investment tracking. The main component is a sophisticated dashboard system for monitoring crypto investments, yields, and portfolio performance.

## Core Architecture

### Web3 Farm Dashboard System
Located in `web3/farm/`, this is a modular JavaScript-based dashboard system for tracking DeFi investments:

**Main Entry Point:**
- `dashboard.md` - Primary dashboard interface using Obsidian DataviewJS

**Core Library Modules (in `web3/farm/lib/`):**
- `dashboard-config.js` - Configuration management and system settings
- `dashboard-utils.js` - Utility functions for date/number formatting and colors
- `dashboard-data-loader.js` - Data loading, parsing, and caching system
- `dashboard-calculator.js` - Financial calculations (APR, earnings, USDT conversions)
- `dashboard-table-builder.js` - Table generation for risk and date dimensions
- `dashboard-renderer.js` - HTML rendering engine with fixed table headers
- `dashboard-ui.js` - User interface controls and event handling
- `dashboard-core.js` - Core API and system initialization

**Data Structure:**
- `history/doing/` - Active investment projects
- `history/done/` - Completed investment projects
- `data/` - Configuration files and price data
- `pools/` - Investment pool definitions (CEX and on-chain)

### Key Features
- **Dual-dimension analysis**: Risk level and date-based views
- **Real-time calculations**: APR, earnings, investment days
- **Smart caching**: Performance optimization for large datasets
- **Modular architecture**: 8 independent JavaScript modules
- **Price integration**: Binance and CoinGecko API integration

## Common Commands

### Price Data Updates
```bash
cd web3/farm
python update_prices.py
```
This script fetches historical token prices from Binance and CoinGecko APIs, caches them locally, and updates the `data/token_prices.md` file.

### Dashboard Development
- Edit library modules in `web3/farm/lib/` for functionality changes
- Modify `dashboard.md` only for entry point changes
- See `README_dashboard.md` for detailed development guidelines

## Development Guidelines

### File Organization
- **Investment tracking**: Use YAML frontmatter in `.md` files in `history/` folders
- **Configuration**: Store settings in `data/` directory
- **Code changes**: Modify appropriate library modules, not the main dashboard file

### Data Format
Investment files use this YAML structure:
```yaml
---
Protocol: [Protocol Name]
Type: [Investment Type]
Unit: [Token Symbol]
Risk: [Risk Level]
# ... other fields
---
```

### Module Dependencies
The dashboard loads modules in a specific order due to dependencies:
1. Config → Utils → Data Loader → Calculator → Table Builder → Renderer → UI → Core

### Caching System
- Global cache variables: `DashboardTableData`, `DashboardDateBaseData`, `DashboardDateTableData`
- Cache is automatically managed by the data loader module
- Clear cache: `window.DashboardCore.DataLoader.Cache.clear()`

## Important Notes

- This is an Obsidian vault, not a standalone web application
- All code runs within Obsidian's DataviewJS environment
- The system handles sensitive financial data - maintain security best practices
- Price data is cached locally to avoid API rate limits
- The dashboard supports responsive design for different screen sizes