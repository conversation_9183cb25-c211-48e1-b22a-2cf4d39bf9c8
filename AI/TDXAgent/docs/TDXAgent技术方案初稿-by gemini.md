

### **1. 方案概述 (Executive Summary)**

本项目旨在开发一款名为 **TDXAgent** 的个人信息 AI 助理。其核心功能是自动获取用户在 **Twitter (X)**、**Telegram** 上的个人信息流，并提供一种**安全模式**来处理 **Discord** 数据。系统将利用大型语言模型（LLM）对收集到的信息进行智能分析与总结，最终以结构化的 Markdown 文件形式呈现给用户，旨在极大节省用户筛选和阅读信息的时间与精力。

本方案基于**模块化**和**风险规避**原则设计，优先采用免费、稳定且风险可控的技术路径，并为用户提供清晰的风险选项。

### **2. 整体技术架构**

系统将遵循下图所示的模块化流程，实现从配置读取到报告生成的全自动化处理。

代码段

```
graph TD
    A[用户] --> B(启动 TDXAgent);
    B --> C{读取 `config.yaml` 配置};
    C --> D[1. 数据获取模块];
    D -- 数据流 --> E[2. 数据存储模块 (JSONL)];
    E -- 按需读取 --> F[3. 智能处理模块 (LLM)];
    F -- 总结内容 --> G[4. 输出模块];
    G -- 生成 `YYYY-MM-DD_HH-MM-SS.md` --> H[Markdown 报告];

    subgraph D [1. 数据获取模块]
        D1[Twitter Scraper (Playwright)]
        D2[Telegram Client (Telethon)]
        D3[Discord Processor (安全模式/高风险模式)]
    end

    subgraph E [2. 数据存储模块 (JSONL)]
        E1[📂 /twitter/YYYY-MM-DD.jsonl]
        E2[📂 /telegram/YYYY-MM-DD.jsonl]
        E3[📂 /discord/YYYY-MM-DD.jsonl]
    end

    subgraph F [3. 智能处理模块 (LLM)]
        F1[Prompt 管理]
        F2{LLM API 适配器 (OpenAI/Gemini)}
        F3[API 接口调用]
    end
```

---

### **3. 核心模块详细设计**

#### **3.1 配置模块 (`config.yaml`)**

系统所有可变参数均由此文件管理，方便用户自定义。

YAML

```
# 全局设置
settings:
  # 如果不指定时间范围，默认获取过去 n 小时的数据
  default_hours_to_fetch: 12
  # 数据存储的根目录
  data_directory: "TDXAgent_Data"

# 数据获取平台配置
platforms:
  twitter:
    enabled: true
  telegram:
    enabled: true
    # 要排除的群组/频道名称(完全匹配)
    group_blacklist:
      - "广告群组A"
      - "无关频道B"
  discord:
    # 'safe' (推荐) 或 'experimental' (高风险)
    mode: 'safe' 
    enabled: true

# LLM 配置
llm:
  # 一次发送给 LLM 的数据条数
  batch_size: 50 
  # 'openai' 或 'gemini'
  provider: 'openai'
  
  openai:
    api_key: 'sk-...'
    base_url: 'https://api.openai.com/v1' # 可配置为兼容接口
    model: 'gpt-4o-mini'
    
  gemini:
    api_key: '...'
    model: 'gemini-1.5-flash'
    
# 各平台专属的提示词 (Prompt)
prompts:
  twitter: |
    你是一位顶级的社交媒体分析师。请根据以下我关注的推文内容，帮我总结出 3-5 个最热门或最有趣的讨论话题。请以要点的形式呈现，并对每个话题进行简要阐述。
    ---
    {data}
    
  telegram: |
    你是一个高效的群聊助理。请从以下聊天记录中，提炼出重要的通知公告、关键的技术讨论、以及大家正在热议的有趣话题。忽略日常闲聊。
    ---
    {data}
    
  discord: |
    你是一个游戏和社区分析专家。请根据以下 Discord 聊天内容，总结出重要的游戏更新、社区活动安排和核心玩家的讨论焦点。
    ---
    {data}
```

#### **3.2 数据获取模块**

此模块是项目的核心，针对各平台采用最佳实践策略。

- **Twitter (X)**
    
    - **技术**: **Playwright** + **Cookie 认证**。
        
    - **流程**:
        
        1. 首次运行，程序将引导用户在 Playwright 启动的浏览器中**手动登录**。
            
        2. 登录成功后，程序自动保存当前会话的 **Cookie** 到本地文件。
            
        3. 后续运行将直接加载 Cookie，实现**免密、免扫码登录**，极大降低被识别为机器人的风险。
            
        4. 通过模拟人类滚动、随机化延迟等方式，抓取 "Following" 和 "For You" 页面的推文数据。
            
    - **风险管理**: 此方法虽非零风险，但通过模拟真人行为和避免频繁密码登录，已是当前最可靠的免费方案。
        
- **Telegram**
    
    - **技术**: **Telethon** 客户端库。
        
    - **流程**:
        
        1. 用户在 `config.yaml` 中配置从 `my.telegram.org` 获取的 `api_id` 和 `api_hash`。
            
        2. 首次运行，Telethon 会在命令行中请求手机号和验证码，生成本地的 `.session` 文件，实现自动登录。
            
        3. 程序将获取所有对话，并根据 `group_blacklist` 进行过滤，然后抓取指定时间范围内的消息。
            
    - **风险管理**: **安全、稳定**。这是官方支持的客户端操作方式。
        
- **Discord**
    
    - **技术**: 提供**两种模式**供用户在 `config.yaml` 中选择。
        
    - **模式 A: `safe` (安全模式 - 默认推荐)** 🛡️
        
        1. **原理**: 利用官方的**数据导出**功能，零风险。
            
        2. **流程**: 程序将引导用户手动前往 Discord 设置请求数据包。用户下载数据包 (`.zip`) 后，将其放入指定目录，程序会自动解压并解析所有历史消息。
            
        3. **优点**: 100% 安全。
            
        4. **缺点**: 非实时，依赖手动操作。
            
    - **模式 B: `experimental` (高风险实验模式)** 🧪
        
        1. **原理**: 使用 **Playwright** 进行 Web 抓取。
            
        2. **流程**: 与 Twitter 类似，通过 Cookie 复用登录后，抓取指定服务器的聊天内容。
            
        3. **风险管理**: 此行为**严重违反 Discord 服务条款**，**极有可能导致账户被永久封禁**。启用此模式前，程序必须在终端显示强烈警告，并要求用户输入特定确认信息方可继续。
            

#### **3.3 数据存储模块**

- **技术**: **JSON Lines (JSONL) 文件系统**。
    
- **结构**: 数据将按 `平台/日期` 的多级目录结构存放。
    
    ```
    TDXAgent_Data/
    └── twitter/
        └── 2025-07-14.jsonl
    ```
    
- **数据格式**: 每个 `.jsonl` 文件中的每一行都是一个独立的、统一格式的 JSON 对象。
    
    JSON
    
    ```
    {"id": "msg_12345", "platform": "twitter", "author_name": "UserA", "content": "This is a tweet.", "message_url": "https://x.com/...", "media_urls": ["https://.../img.jpg"], "posted_at": "2025-07-14T12:30:00Z"}
    {"id": "msg_67890", "platform": "telegram", "author_name": "UserB", "content": "Hello everyone!", "message_url": "https://t.me/...", "media_urls": [], "posted_at": "2025-07-14T12:32:00Z"}
    ```
    
- **优点**: 结构直观，人类可读，写入简单，便于按天归档和迁移。非常适合本项目顺序读写的核心场景。
    

#### **3.4 智能处理与输出模块**

- **LLM 适配器**: 设计一个通用的 LLM 调用接口，内置 `OpenAIProvider` 和 `GeminiProvider` 两个实现。程序根据 `config.yaml` 的 `provider` 配置自动选择。
    
- **批处理**: 程序会根据当前时间范围，从 `JSONL` 文件中读取相应数据。数据将按 `batch_size` 分割成块。
    
- **Prompt 构建**: 将每一批数据块填入 `config.yaml` 中对应平台的 `prompts` 模板，形成完整的请求。
    
- **输出**:
    
    1. 收集所有批次 LLM 返回的总结内容。
        
    2. 将最终的完整总结内容存为 Markdown 文件。
        
    3. 文件名以执行时间命名，如 `Summary_2025-07-14_13-30-05.md`，方便用户查找。
        
