
现在我有一个大的csv文件，编码为utf8，文件分隔符为逗号，有三列数据且带表头
现在我需要将这个csv文件切分为大小差不多的子csv文件，并且每个csv文件都带有一个chk校验文件，子csv和chk文件的生成规则如下，请你根据以下规则帮我写一个数据处理脚本

子csv数据文件规则
1. 单文件大小: 单个文件大小500MB，确保拆分后每行都是完整的数据
2. 文件名: MeituanLabelReq_0400000000-001-yyyymmdd-${2位序号}.bin, 2位序列号从01开始递增
3. utf8编码，不带表头
4. 行分隔符为0D0A
5. 列分隔符为char27

chk校验文件规则:
1. 文件名: 同bin数据文件名，后缀改为.chk
2. 第一行
    1. 应用标识: 20个字符，不足时前补空格，值为 BF-BDS(SZXFH)
    2. 应用名: 20个字符，不足时前补空格，值为 BF-BDS(SZXFH)
    3. 文件传输日期: 8个字符，格式为yyyymmdd
    4. 场次: 默认为3个空格
    5. 传输文件个数: 7个字符，不足时前补空格，值为1
    6. 备用字段: 默认为168个空格
3. 第二行
    1. 文件名: 100个字符，不足时前补空格
    2. 文件记录数: 12个字符，不足时前补空格
    3. 文件总大小: 20个字符，文件大小单位为字节数，不足时前补空格
    4. 文件创建时间: 默认14个空格
    5. 文件状态: 默认1个空格
    6. 备用字段: 默认109个空格
其中chk文件编码为ASCII、换行符为0D0A