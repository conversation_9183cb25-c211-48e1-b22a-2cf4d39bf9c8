

    docker run -d -v /opt/meituan/apps/ollama:/root/.ollama -p 8000:11434 --name ollama ollama/ollama
    docker exec -it ollama ollama pull mxbai-embed-large:latest

    curl http://*************:11434/api/embed -d '{"model": "mxbai-embed-large", "input": "Represent this sentence for searching relevant passages: 中国的首都是北京"}'

    GLM-Z1-Flash,30
    glm-4-flash,60
    LongCat-MoE-3B-32K-Chat,25
    gpt-4o-mini,200
    text-embedding-miffy-002,60

    测试：friday
    正式：商汤r1、gemini 2.0 flash lite
    本地的qwen2.5 14b可以跑，速度慢
    服务器的cpu 可以跑，特别慢
    -----


    config = {
        "vector_store": {
            "provider": "qdrant",
            "config": {
                "collection_name": "test",
                "host": "localhost",
                "port": 6333,
            }
        },
        # "llm": {
        #     "provider": "openai",
        #     "config": {

        #         "model": "gpt-4o-mini",
        #         "temperature": 0.1,
        #         "max_tokens": 128 * 1024,
        #         "openai_base_url": "https://api.tu-zi.com/v1",
        #         "api_key": "sk-QTVOFhVKAPLUb7BSxuUMcC6fwUnsyZmWDMxfxE1h6lPpVl8g",
        #     }
        # },
        # models/gemini-2.5-flash-lite-preview-06-17 tpm: 250,000, RPD: 500, RPM: 15
        # models/gemini-2.0-flash-lite tpm: 1,000,000 rpd: 1500, RPM: 30
        "llm": {
            "provider": "openai",
            "config": {
                "model": "models/gemini-2.0-flash-lite",
                "temperature": 0.1,
                "api_key": "AIzaSyAvJAMyULpUYUWknX9dH8cxlCSt_EYi8d0",
                "openai_base_url": "https://www.xiaohei.tech"
            }
        },
        # "llm": {
        #     "provider": "openai",
        #     "config": {
        #         "model": "DeepSeek-V3",
        #         "temperature": 0.1,
        #         "api_key": "sk-JIpCgghOdm7QWvTQgwnLE1uinBVuktjQ",
        #         "openai_base_url": "https://api.sensenova.cn/compatible-mode/v1"
        #     }
        # },
        "llm": {
            "provider": "openai",
            "config": {
                "model": "LongCat-MoE-3B-32K-Chat",
                "temperature": 0.1,
                "api_key": "21897964815046574094",
                "openai_base_url": "https://aigc.sankuai.com/v1/openai/native"
            }
        },
        # "llm": {
        #     "provider": "ollama",
        #     "config": {
        #         "model": "qwen2.5:14b",
        #         "temperature": 0.1,
        #         "max_tokens": 32768,
        #         "ollama_base_url": "http://*************:8000"
        #     }
        # },
        # "llm": {
        #     "provider": "ollama",
        #     "config": {
        #         "model": "qwen2.5:7b",
        #         "temperature": 0.1
        #     }
        # },
        # "embedder": {
        #     "provider": "ollama",
        #     "config": {
        #         "model": "mxbai-embed-large",
        #         "embedding_dims": 1024,
        #         "ollama_base_url": "http://*************:8000"
        #     }
        # },
        "embedder": {
            "provider": "openai",
            "config": {
                "model": "models/text-embedding-004",
                "embedding_dims": 768,
                "api_key": "AIzaSyAvJAMyULpUYUWknX9dH8cxlCSt_EYi8d0",
                "openai_base_url": "https://www.xiaohei.tech"
            }
        },
        # "embedder": {
        #     "provider": "openai",
        #     "config": {
        #         "model": "text-embedding-miffy-002",
        #         "embedding_dims": 768,
        #         "api_key": "21897964815046574094",
        #         "openai_base_url": "https://aigc.sankuai.com/v1/openai/native"
        #     }
        # },
        "graph_store": {
            "provider": "neo4j",
            "config": {
                "url": "neo4j://127.0.0.1:7687",
                "username": "neo4j",
                "password": "password"
            }
        }
        # "history_db_path": "/path/to/history.db",
        # "version": "v1.1",
        # "custom_fact_extraction_prompt": "Optional custom prompt for fact extraction for memory",
        # "custom_update_memory_prompt": "Optional custom prompt for update memory"
    }