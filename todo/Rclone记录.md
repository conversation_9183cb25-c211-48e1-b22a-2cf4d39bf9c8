
```shell
rclone bisync /Users/<USER>/icloud/Documents/ icloud:Documents --resync -v --dry-run
rclone bisync /Users/<USER>/icloud/Downloads/ icloud:Downloads --resync -v --dry-run
rclone bisync /Users/<USER>/icloud/Desktop/ icloud:Desktop --resync -v --dry-run
rclone bisync /Users/<USER>/icloud/Obsidian/ icloud:Obsidian --resync -v --dry-run

*/5 * * * * /usr/local/bin/rclone bisync /Users/<USER>/icloud/Documents/ icloud:Documents -v --log-file /Users/<USER>/rclone/rclone_sync_Documents.log
*/5 * * * * /usr/local/bin/rclone bisync /Users/<USER>/icloud/Downloads/ icloud:Downloads -v --log-file /Users/<USER>/rclone/rclone_sync_Downloads.log
*/5 * * * * /usr/local/bin/rclone bisync /Users/<USER>/icloud/Desktop/ icloud:Desktop -v --log-file /Users/<USER>/rclone/rclone_sync_Desktop.log
*/5 * * * * /usr/local/bin/rclone bisync /Users/<USER>/icloud/Obsidian/ icloud:Obsidian -v --log-file /Users/<USER>/rclone/rclone_sync_Obsidian.log

rclone mount icloud:Documents /Users/<USER>/icloud/Documents/ --vfs-cache-mode writes --daemon --log-file /Users/<USER>/rclone/rclone_mount_Documents.log

rclone mount icloud:Downloads /Users/<USER>/icloud/Downloads/ --vfs-cache-mode writes --daemon --log-file /Users/<USER>/rclone/rclone_mount_Downloads.log

rclone mount icloud:Desktop /Users/<USER>/icloud/Desktop/ --vfs-cache-mode writes --daemon --log-file /Users/<USER>/rclone/rclone_mount_Desktop.log
```



