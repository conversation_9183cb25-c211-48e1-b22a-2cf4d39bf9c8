
## Dashboard 优化
- [x] 总收益、日收益、当前 apr、当前 apr 总比等字段若为负数则字体显示红色、正数显示为绿色 ✅ 2025-07-11
- [x] 各个风险等级行的字体显示加粗 ✅ 2025-07-11
- [x] 日期视图中状态列 doing 前添加绿色标记、如果是 done 则添加灰色标记 ✅ 2025-07-11
- [x] 日期视图中总收益、日收益、当前 apr、当前 apr 总比等字段若为负数则字体显示红色、正数显示为绿色 ✅ 2025-07-11
- [x] 日期视图最下方一行添加类似风险视图中的总计行 ✅ 2025-07-11
- [x] 日期视图中的日粒度为什么总收益都是 0、投资天数都是 1 天，这个数据是怎么计算出来的？ ✅ 2025-07-11
	- [x] 投资天数的开始投资日期应该不受当前统计维度的影响，始终使用第一个 date 的数据，计数投资日期才需要根据当前时间统计维度进行限制，这样显示的投资天数才是准确的 ✅ 2025-07-11
- [x] 制定一个总资产收益率余额 150%，by 周、月进行汇总统计并计算与预期的 gap 有多少差异 ✅ 2025-07-11
- [x] 所有持仓更新 ✅ 2025-07-12

# Work todo

- [x] Gemini CLI 模型修改：/opt/homebrew/lib/node_modules/@google/gemini-cli/node_modules/@google/gemini-cli-core/dist/src/config/models.js ✅ 2025-07-13