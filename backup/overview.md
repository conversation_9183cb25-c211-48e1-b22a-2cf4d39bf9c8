## Weekly

```dataviewjs
// --- 配置 ---
const FOLDER_PATH = '"web3/farm/history"';
const PRICES_FILE_PATH = 'web3/farm/data/token_prices';

// --- 脚本开始 ---

// 1. 读取并解析结构化的价格文件
const pricesPage = dv.page(PRICES_FILE_PATH);
let priceMap = new Map();
let hasPriceFileError = false;

if (!pricesPage || !pricesPage.file.lists || pricesPage.file.lists.length === 0) {
    dv.paragraph("**错误:** 价格文件未找到或格式不正确! 请先运行 `update_prices.py` 脚本。");
    hasPriceFileError = true;
} else {
    // 将价格加载到一个嵌套的 Map 中以便快速查询: Map<Unit, Map<Date, Price>>
    for (const item of pricesPage.file.lists) {
        const tokenMatch = item.text.match(/token:\s*(.*)/);
        const dateMatch = item.text.match(/date:\s*(.*)/);
        const priceMatch = item.text.match(/price:\s*(.*)/);

        const token = tokenMatch ? tokenMatch[1].trim() : undefined;
        const date = dateMatch ? dateMatch[1].trim() : undefined;
        const price = priceMatch ? parseFloat(priceMatch[1].trim()) : undefined;

        if (!token || !date || price == null) {
            continue; // 确保所有字段都存在
        }
        
        const unit = token.toUpperCase();
        if (!priceMap.has(unit)) {
            priceMap.set(unit, new Map());
        }
        // 使用格式化字符串作为 Map 的键
        priceMap.get(unit).set(dv.date(date).toFormat("yyyy-MM-dd"), price);
    }

    // 为稳定币添加默认价格 (确保它们在 priceMap 中，即使 token_prices.md 中没有)
    // 它们的汇率在计算时会默认为 1.0
    priceMap.set('USDT', priceMap.get('USDT') || new Map());
    priceMap.set('USDC', priceMap.get('USDC') || new Map());
    priceMap.set('DAI', priceMap.get('DAI') || new Map());
}

let allProfits = [];

// 只有在没有价格文件错误时才执行后续逻辑
if (!hasPriceFileError) {
    // 2. 计算所有独立的收益记录
    const pages = dv.pages(FOLDER_PATH);
    
    for (const page of pages) {
        const unit = (page.Unit || 'USDT').toUpperCase();
        const lists = page.file.lists;
        if (!lists || lists.length === 0) continue;

        const dateMap = new Map();
        for (const item of lists) {
            if (item.date && item.balance != null) {
                const dateStr = item.date.toString();
                const formattedDateStr = dateStr.substring(0, 4) + "-" + dateStr.substring(4, 6) + "-" + dateStr.substring(6, 8);
                const dateObject = dv.date(formattedDateStr);
                if (dateObject) {
                    dateMap.set(dateObject, item.balance);
                }
            }
        }

        const sortedDates = Array.from(dateMap.keys()).sort((a, b) => a - b);

        // 从 date 列表获取投资金额（第一条记录的 balance）
        const investAmount = sortedDates.length > 0 ? dateMap.get(sortedDates[0]) : null;

        for (let i = 0; i < sortedDates.length; i++) {
            const currentDate = sortedDates[i];
            const currentBalance = dateMap.get(currentDate);
            
            // 确定用于计算收益的“上一次余额”
            // 如果是第一条记录，则使用初始投资额（第一条记录的 balance）
            // 否则，使用排序后上一条记录的余额
            const prevBalance = (i === 0) 
                ? investAmount 
                : dateMap.get(sortedDates[i-1]);

            // 如果没有可用于比较的余额，则跳过
            if (prevBalance == null) continue;

            const formattedCurrentDate = currentDate.toFormat("yyyy-MM-dd");
            // 获取当天的汇率，如果找不到则默认为 1
            const rate = priceMap.get(unit)?.get(formattedCurrentDate) || 1.0;
            
            const profitInNative = currentBalance - prevBalance;
            const profitInUsdt = profitInNative * rate;

            if (profitInUsdt !== 0) {
                // 提取文件名（去掉路径和扩展名）
                const fileName = page.file.name;
                allProfits.push({
                    date: currentDate,
                    project: fileName,
                    file: page.file,
                    profit: profitInUsdt,
                });
            }
        }
    }
}

// Helper function to generate the profit table based on filter type
function generateProfitTable(dv, profits, filterType) {
    let filteredProfits = profits;
    const now = dv.date('now');

    if (filterType === 'today') {
        const startOfDay = now.startOf('day');
        filteredProfits = profits.filter(p => p.date >= startOfDay && p.date <= now);
    } else if (filterType === 'weekly') {
        const sevenDaysAgo = now.minus({ days: 7 });
        filteredProfits = profits.filter(p => p.date >= sevenDaysAgo);
    } else if (filterType === 'monthly') {
        const startOfMonth = now.startOf('month');
        filteredProfits = profits.filter(p => p.date >= startOfMonth && p.date <= now);
    } else if (filterType === 'yearly') {
        const startOfYear = now.startOf('year');
        filteredProfits = profits.filter(p => p.date >= startOfYear && p.date <= now);
    }
    // 'total' filter means no filtering, so filteredProfits remains allProfits

    // Aggregation and rendering logic
    const dailySummary = dv.array(filteredProfits)
        .groupBy(p => p.date)
        .map(g => ({
            displayKey: g.key.toFormat("yyyy-MM-dd"),
            projectBreakdown: g.rows.map(r => `[[${r.project}]] (${(Math.round(r.profit * 100) / 100).toFixed(2)} USDT)`).join('<br>'),
            totalProfit: Math.round(g.rows.map(r => r.profit).sum() * 100) / 100
        }))
        .sort(d => d.date, 'desc');

    const projectSummary = dv.array(filteredProfits)
        .groupBy(p => p.project)
        .map(g => ({
            displayKey: `[[${g.rows[0].project}]]`,
            projectBreakdown: "",
            totalProfit: Math.round(g.rows.map(r => r.profit).sum() * 100) / 100
        }))
        .sort(p => p.totalProfit, 'desc');

    const grandTotal = Math.round(dv.array(filteredProfits).map(p => p.profit).sum() * 100) / 100;

    const tableData = [];

    dailySummary.forEach((d, index) => {
        tableData.push([d.displayKey, d.projectBreakdown, d.totalProfit.toFixed(2)]);
        if (index < dailySummary.length - 1) tableData.push(["---", "---", "---"]);
    });

    if (dailySummary.length > 0 && projectSummary.length > 0) tableData.push(["---", "---", "---"]);

    projectSummary.forEach((p, index) => {
        tableData.push([p.displayKey, p.projectBreakdown, p.totalProfit.toFixed(2)]);
        if (index < projectSummary.length - 1) tableData.push(["---", "---", "---"]);
    });

    if (projectSummary.length > 0) tableData.push(["---", "---", "---"]);

    tableData.push(["**总收益合计 (USDT)**", "", `**${grandTotal.toFixed(2)}**`]);

    dv.table(
        ["日期 / 项目", "各项目收益 (USDT)", "总收益 (USDT)"],
        tableData
    );
}

if (!hasPriceFileError) {
    generateProfitTable(dv, allProfits, 'weekly');
}
```

## Total-by day

```dataviewjs
// --- 配置 ---
const FOLDER_PATH = '"web3/farm/history"';
const PRICES_FILE_PATH = 'web3/farm/data/token_prices';

// --- 脚本开始 ---

// 1. 读取并解析结构化的价格文件
const pricesPage = dv.page(PRICES_FILE_PATH);
let priceMap = new Map();
let hasPriceFileError = false;

if (!pricesPage || !pricesPage.file.lists || pricesPage.file.lists.length === 0) {
    dv.paragraph("**错误:** 价格文件未找到或格式不正确! 请先运行 `update_prices.py` 脚本。");
    hasPriceFileError = true;
} else {
    // 将价格加载到一个嵌套的 Map 中以便快速查询: Map<Unit, Map<Date, Price>>
    for (const item of pricesPage.file.lists) {
        const tokenMatch = item.text.match(/token:\s*(.*)/);
        const dateMatch = item.text.match(/date:\s*(.*)/);
        const priceMatch = item.text.match(/price:\s*(.*)/);

        const token = tokenMatch ? tokenMatch[1].trim() : undefined;
        const date = dateMatch ? dateMatch[1].trim() : undefined;
        const price = priceMatch ? parseFloat(priceMatch[1].trim()) : undefined;

        if (!token || !date || price == null) {
            continue; // 确保所有字段都存在
        }
        
        const unit = token.toUpperCase();
        if (!priceMap.has(unit)) {
            priceMap.set(unit, new Map());
        }
        // 使用格式化字符串作为 Map 的键
        priceMap.get(unit).set(dv.date(date).toFormat("yyyy-MM-dd"), price);
    }

    // 为稳定币添加默认价格 (确保它们在 priceMap 中，即使 token_prices.md 中没有)
    // 它们的汇率在计算时会默认为 1.0
    priceMap.set('USDT', priceMap.get('USDT') || new Map());
    priceMap.set('USDC', priceMap.get('USDC') || new Map());
    priceMap.set('DAI', priceMap.get('DAI') || new Map());
}

let allProfits = [];

// 只有在没有价格文件错误时才执行后续逻辑
if (!hasPriceFileError) {
    // 2. 计算所有独立的收益记录
    const pages = dv.pages(FOLDER_PATH);
    
    for (const page of pages) {
        const unit = (page.Unit || 'USDT').toUpperCase();
        const lists = page.file.lists;
        if (!lists || lists.length === 0) continue;

        const dateMap = new Map();
        for (const item of lists) {
            if (item.date && item.balance != null) {
                const dateStr = item.date.toString();
                const formattedDateStr = dateStr.substring(0, 4) + "-" + dateStr.substring(4, 6) + "-" + dateStr.substring(6, 8);
                const dateObject = dv.date(formattedDateStr);
                if (dateObject) {
                    dateMap.set(dateObject, item.balance);
                }
            }
        }

        const sortedDates = Array.from(dateMap.keys()).sort((a, b) => a - b);

        // 从 date 列表获取投资金额（第一条记录的 balance）
        const investAmount = sortedDates.length > 0 ? dateMap.get(sortedDates[0]) : null;

        for (let i = 0; i < sortedDates.length; i++) {
            const currentDate = sortedDates[i];
            const currentBalance = dateMap.get(currentDate);
            
            // 确定用于计算收益的“上一次余额”
            // 如果是第一条记录，则使用初始投资额（第一条记录的 balance）
            // 否则，使用排序后上一条记录的余额
            const prevBalance = (i === 0) 
                ? investAmount 
                : dateMap.get(sortedDates[i-1]);

            // 如果没有可用于比较的余额，则跳过
            if (prevBalance == null) continue;

            const formattedCurrentDate = currentDate.toFormat("yyyy-MM-dd");
            // 获取当天的汇率，如果找不到则默认为 1
            const rate = priceMap.get(unit)?.get(formattedCurrentDate) || 1.0;
            
            const profitInNative = currentBalance - prevBalance;
            const profitInUsdt = profitInNative * rate;

            if (profitInUsdt !== 0) {
                // 提取文件名（去掉路径和扩展名）
                const fileName = page.file.name;
                allProfits.push({
                    date: currentDate,
                    project: fileName,
                    file: page.file,
                    profit: profitInUsdt,
                });
            }
        }
    }
}

// Helper function to generate the profit table based on filter type with pagination
function generateProfitTableWithPagination(dv, profits, filterType, sectionId) {
    let filteredProfits = profits;
    const now = dv.date('now');

    if (filterType === 'today') {
        const startOfDay = now.startOf('day');
        filteredProfits = profits.filter(p => p.date >= startOfDay && p.date <= now);
    } else if (filterType === 'weekly') {
        const sevenDaysAgo = now.minus({ days: 7 });
        filteredProfits = profits.filter(p => p.date >= sevenDaysAgo);
    } else if (filterType === 'monthly') {
        const startOfMonth = now.startOf('month');
        filteredProfits = profits.filter(p => p.date >= startOfMonth && p.date <= now);
    } else if (filterType === 'yearly') {
        const startOfYear = now.startOf('year');
        filteredProfits = profits.filter(p => p.date >= startOfYear && p.date <= now);
    }
    // 'total' filter means no filtering, so filteredProfits remains allProfits

    // Aggregation and rendering logic
    const dailySummary = dv.array(filteredProfits)
        .groupBy(p => p.date)
        .map(g => ({
            displayKey: g.key.toFormat("yyyy-MM-dd"),
            projectBreakdown: g.rows.map(r => `[[${r.project}]] (${(Math.round(r.profit * 100) / 100).toFixed(2)} USDT)`).join('<br>'),
            totalProfit: Math.round(g.rows.map(r => r.profit).sum() * 100) / 100,
            sortDate: g.key
        }))
        .sort(d => d.sortDate, 'desc');

    const projectSummary = dv.array(filteredProfits)
        .groupBy(p => p.project)
        .map(g => ({
            displayKey: `[[${g.rows[0].project}]]`,
            projectBreakdown: "",
            totalProfit: Math.round(g.rows.map(r => r.profit).sum() * 100) / 100
        }))
        .sort(p => p.totalProfit, 'desc');

    const grandTotal = Math.round(dv.array(filteredProfits).map(p => p.profit).sum() * 100) / 100;

    // 准备纯数据行（不包含分隔符）
    const dataRows = [];
    dailySummary.forEach((d) => {
        dataRows.push([d.displayKey, d.projectBreakdown, d.totalProfit.toFixed(2)]);
    });

    // 总收益行
    const totalRow = ["**总收益合计 (USDT)**", "", `**${grandTotal.toFixed(2)}**`];

    // 分页配置
    const ITEMS_PER_PAGE = 10;
    const storageKey = `overview${sectionId}Page`;

    // 一次性清除错误的分页缓存（修复后可以删除这行）
    localStorage.removeItem(storageKey);

    let currentPage = parseInt(localStorage.getItem(storageKey)) || 1;

    // 验证并修正当前页数
    const maxPages = Math.ceil(dataRows.length / ITEMS_PER_PAGE) || 1;
    if (currentPage > maxPages) {
        currentPage = 1;
        localStorage.setItem(storageKey, '1');
    }

    // 分页逻辑
    const createPagination = (totalItems, currentPage, itemsPerPage) => {
        const totalPages = Math.ceil(totalItems / itemsPerPage);
        const startIndex = (currentPage - 1) * itemsPerPage;
        const endIndex = Math.min(startIndex + itemsPerPage, totalItems);

        return {
            totalPages,
            currentPage,
            startIndex,
            endIndex,
            hasPrev: currentPage > 1,
            hasNext: currentPage < totalPages
        };
    };

    // 容器变量
    let tableContainer = null;
    let paginationContainer = null;

    const renderTable = (processedData) => {
        // 初始化表格容器
        if (!tableContainer) {
            tableContainer = dv.el("div", "", {
                attr: { id: `${sectionId}TableContainer` }
            });
        }

        // 清空现有表格内容
        tableContainer.innerHTML = '';

        // 创建表格元素
        const table = dv.el("table", "", {
            container: tableContainer,
            attr: {
                style: "width: 100%; border-collapse: collapse; margin: 0; font-size: var(--font-text-size);"
            }
        });

        // 创建表头
        const thead = dv.el("thead", "", { container: table });
        const headerRow = dv.el("tr", "", { container: thead });

        const headers = ["日期 / 项目", "各项目收益 (USDT)", "总收益 (USDT)"];
        headers.forEach(header => {
            dv.el("th", header, {
                container: headerRow,
                attr: {
                    style: "border: 1px solid var(--table-border-color); padding: 6px 8px; background-color: var(--table-header-background); text-align: left; font-weight: var(--font-weight-bold); color: var(--table-header-color);"
                }
            });
        });

        // 创建表体
        const tbody = dv.el("tbody", "", { container: table });

        processedData.forEach((row, rowIndex) => {
            // 检查是否为分隔行
            const isSeparatorRow = row.every(cell => cell === "---");

            const tr = dv.el("tr", "", { container: tbody });

            if (isSeparatorRow) {
                // 渲染分隔行
                const td = dv.el("td", "", {
                    container: tr,
                    attr: {
                        colspan: "3",
                        style: "border: none; padding: 0; height: 1px;"
                    }
                });

                // 创建分隔线
                dv.el("hr", "", {
                    container: td,
                    attr: {
                        style: "margin: 4px 0; border: none; border-top: 1px solid var(--table-border-color); opacity: 0.3;"
                    }
                });
            } else {
                // 渲染普通数据行
                row.forEach((cell, cellIndex) => {
                    const td = dv.el("td", "", {
                        container: tr,
                        attr: {
                            style: "border: 1px solid var(--table-border-color); padding: 6px 8px; color: var(--table-text-color);"
                        }
                    });

                    // 处理包含链接和换行的内容
                    if (typeof cell === 'string') {
                        let cellContent = cell;

                        // 第一列（项目）需要转换为链接
                        if (cellIndex === 0) {
                            const linkMatch = cell.match(/\[\[([^\]]+)\]\]/);
                            if (linkMatch) {
                                const fileName = linkMatch[1];
                                cellContent = `<a href="obsidian://open?vault=Guantik&file=${encodeURIComponent('web3/farm/history/' + fileName)}" style="color: var(--link-color); text-decoration: none;">${fileName}</a>`;
                            }
                        } else {
                            // 处理其他列的链接
                            cellContent = cell.replace(/\[\[([^\]]+)\]\]/g, (match, fileName) => {
                                return `<a href="obsidian://open?vault=Guantik&file=${encodeURIComponent('web3/farm/history/' + fileName)}" style="color: var(--link-color); text-decoration: none;">${fileName}</a>`;
                            });
                        }

                        // 处理粗体文本
                        cellContent = cellContent.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

                        // 设置 HTML 内容以支持 <br> 标签和粗体
                        td.innerHTML = cellContent;
                    } else {
                        td.textContent = cell;
                    }
                });
            }
        });
    };

    const renderPagination = () => {
        // 初始化分页容器
        if (!paginationContainer) {
            paginationContainer = dv.el("div", "", {
                attr: {
                    id: `${sectionId}PaginationContainer`,
                    style: "display: flex; justify-content: space-between; align-items: center; margin: 20px 0; padding: 10px; background-color: var(--background-secondary); border-radius: 8px;"
                }
            });
        }

        // 清空现有分页内容
        paginationContainer.innerHTML = '';

        const pagination = createPagination(dataRows.length, currentPage, ITEMS_PER_PAGE);

        // 左侧：统计信息
        const statsDiv = dv.el("div", `📊 第 ${currentPage} / ${pagination.totalPages} 页，共 ${dataRows.length} 条记录`, {
            container: paginationContainer,
            attr: {
                style: "font-weight: bold; color: var(--text-normal);"
            }
        });

        // 右侧：分页按钮
        const buttonsDiv = dv.el("div", "", {
            container: paginationContainer,
            attr: {
                style: "display: flex; gap: 10px; align-items: center;"
            }
        });

        // 上一页按钮
        if (pagination.hasPrev) {
            const prevBtn = dv.el("button", "← 上一页", {
                container: buttonsDiv,
                attr: {
                    style: "padding: 8px 16px; background-color: var(--interactive-accent); color: var(--text-on-accent); border: none; border-radius: 4px; cursor: pointer; font-size: 14px;"
                }
            });
            prevBtn.addEventListener('click', () => {
                currentPage = currentPage - 1;
                localStorage.setItem(storageKey, currentPage.toString());
                updateDisplay();
            });
        }

        // 页码显示
        const pageInfo = dv.el("span", `${currentPage} / ${pagination.totalPages}`, {
            container: buttonsDiv,
            attr: {
                style: "padding: 8px 12px; background-color: var(--background-modifier-border); border-radius: 4px; font-weight: bold;"
            }
        });

        // 下一页按钮
        if (pagination.hasNext) {
            const nextBtn = dv.el("button", "下一页 →", {
                container: buttonsDiv,
                attr: {
                    style: "padding: 8px 16px; background-color: var(--interactive-accent); color: var(--text-on-accent); border: none; border-radius: 4px; cursor: pointer; font-size: 14px;"
                }
            });
            nextBtn.addEventListener('click', () => {
                currentPage = currentPage + 1;
                localStorage.setItem(storageKey, currentPage.toString());
                updateDisplay();
            });
        }
    };

    const updateDisplay = () => {
        // 对纯数据行进行分页
        const pagination = createPagination(dataRows.length, currentPage, ITEMS_PER_PAGE);
        const paginatedDataRows = dataRows.slice(pagination.startIndex, pagination.endIndex);

        // 构建要渲染的表格数据（包含分隔符）
        const tableDataToRender = [];
        paginatedDataRows.forEach((row, index) => {
            tableDataToRender.push(row);
            if (index < paginatedDataRows.length - 1) {
                tableDataToRender.push(["---", "---", "---"]);
            }
        });

        // 在每页末尾添加总收益行
        if (paginatedDataRows.length > 0) {
            tableDataToRender.push(["---", "---", "---"]);
        }
        tableDataToRender.push(totalRow);

        renderTable(tableDataToRender);
        renderPagination();
    };

    // 初始渲染
    updateDisplay();
}

if (!hasPriceFileError) {
    generateProfitTableWithPagination(dv, allProfits, 'total', 'Total');
}
```
