[黑地址查询](https://detrust.bitrace.io/detrust-blacklist/)

Doing：
```dataview
TABLE
  TotalEarned,APR,Chain,Type,InvestDate,InvestAmount,Level,Unit,
  WithdrawDate,WithdrawAmount,Fee,Status,
  InvestDays,DailyEarned,APRnoFee
FROM "web3/farm/history" where status="Doing"
FLATTEN round((date(WithdrawDate) - date(InvestDate)).days,2) as InvestDays
FLATTEN round((WithdrawAmount - InvestAmount),8) as TotalEarned
FLATTEN round((TotalEarned / InvestDays),8) as DailyEarned
FLATTEN round(((((TotalEarned - Fee)/InvestAmount)/InvestDays)*365)*100,2) as APR
FLATTEN round((((TotalEarned/InvestAmount)/InvestDays)*365)*100,2) as APRnoFee
```
Done：
```dataview
TABLE
  TotalEarned,APR,Chain,Type,InvestDate,InvestAmount,Level,Unit,
  WithdrawDate,WithdrawAmount,Fee,Status,
  InvestDays,DailyEarned,APRnoFee
FROM "web3/farm/history" where status="Done"
FLATTEN round((date(WithdrawDate) - date(InvestDate)).days,2) as InvestDays
FLATTEN round((WithdrawAmount - InvestAmount),8) as TotalEarned
FLATTEN round((TotalEarned / InvestDays),8) as DailyEarned
FLATTEN round(((((TotalEarned - Fee)/InvestAmount)/InvestDays)*365)*100,2) as APR
FLATTEN round((((TotalEarned/InvestAmount)/InvestDays)*365)*100,2) as APRnoFee
```
